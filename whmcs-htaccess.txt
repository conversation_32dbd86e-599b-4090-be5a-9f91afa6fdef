# WHMCS Payment Gateway Support - .htaccess
# Upload this file to: /var/www/X-ZoneServers/whmcs/.htaccess

# ======================================
# PAYMENT GATEWAY CSP HEADERS
# ======================================

<IfModule mod_headers.c>
    # Content Security Policy for Payment Gateways - Enhanced for PayPal
    Header always set Content-Security-Policy "default-src 'self' https://www.paypal.com https://*.paypal.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.paypal.com https://*.paypal.com https://js.stripe.com https://www.googletagmanager.com https://www.google-analytics.com https://cdnjs.cloudflare.com https://unpkg.com https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://cdn.tailwindcss.com https://fonts.googleapis.com https://www.paypal.com https://*.paypal.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com https://www.paypal.com https://*.paypal.com; img-src 'self' data: https: https://www.paypal.com https://*.paypal.com; connect-src 'self' https://www.paypal.com https://*.paypal.com https://api.stripe.com https://www.google-analytics.com https://*.paypalobjects.com; frame-src 'self' https://www.paypal.com https://*.paypal.com https://js.stripe.com; child-src 'self' https://www.paypal.com https://*.paypal.com; frame-ancestors 'self'; base-uri 'self'; form-action 'self' https://www.paypal.com https://*.paypal.com; upgrade-insecure-requests"

    # Additional CORS headers for PayPal
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, X-PayPal-*"
    Header always set Access-Control-Allow-Credentials "true"
    
    # Security Headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # CORS for Payment APIs
    Header always set Access-Control-Allow-Origin "https://x-zoneservers.com"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"
</IfModule>

# ======================================
# WHMCS SPECIFIC RULES
# ======================================

# Enable URL rewriting
RewriteEngine On

# Force HTTPS for all WHMCS pages
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# ======================================
# PAYMENT PROCESSING OPTIMIZATION
# ======================================

# Ensure proper MIME types for payment scripts
<FilesMatch "\.(js)$">
    Header set Content-Type "application/javascript; charset=utf-8"
</FilesMatch>

<FilesMatch "\.(css)$">
    Header set Content-Type "text/css; charset=utf-8"
</FilesMatch>

# ======================================
# CACHING FOR PERFORMANCE
# ======================================

<IfModule mod_expires.c>
    ExpiresActive On
    
    # Cache static assets
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    
    # Don't cache dynamic WHMCS pages
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# ======================================
# SECURITY FOR WHMCS
# ======================================

# Block access to sensitive files
<FilesMatch "^(configuration\.php|\.htaccess)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Block access to template source files
<FilesMatch "\.tpl$">
    Order allow,deny
    Deny from all
</FilesMatch>

# ======================================
# CHARSET DEFINITION
# ======================================

AddDefaultCharset UTF-8
