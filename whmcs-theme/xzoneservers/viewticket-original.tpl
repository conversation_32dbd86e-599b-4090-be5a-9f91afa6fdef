{include file="$template/header.tpl"}

<!-- View Ticket Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="ticket" class="w-3 h-3 mr-1.5"></i>
                    Ticket #{$ticketnum}
                </div>
                {if $status eq "Open"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="circle" class="w-3 h-3 mr-1.5 fill-current"></i>
                        {$status}
                    </div>
                {elseif $status eq "Answered"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="message-circle" class="w-3 h-3 mr-1.5"></i>
                        {$status}
                    </div>
                {elseif $status eq "Customer-Reply"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-orange-500/20 to-amber-500/20 border border-orange-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="clock" class="w-3 h-3 mr-1.5"></i>
                        Awaiting Response
                    </div>
                {elseif $status eq "Closed"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-slate-500/20 to-gray-500/20 border border-slate-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="x-circle" class="w-3 h-3 mr-1.5"></i>
                        {$status}
                    </div>
                {else}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="help-circle" class="w-3 h-3 mr-1.5"></i>
                        {$status}
                    </div>
                {/if}
                {if $urgency}
                    {if $urgency eq "High"}
                        <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                            <i data-lucide="alert-triangle" class="w-3 h-3 mr-1.5"></i>
                            High Priority
                        </div>
                    {elseif $urgency eq "Medium"}
                        <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                            <i data-lucide="flag" class="w-3 h-3 mr-1.5"></i>
                            Medium Priority
                        </div>
                    {else}
                        <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-gray-500/20 to-slate-500/20 border border-gray-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                            <i data-lucide="minus" class="w-3 h-3 mr-1.5"></i>
                            Low Priority
                        </div>
                    {/if}
                {/if}
            </div>
            
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div>
                    <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                        <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                            {$subject}
                        </span>
                    </h1>
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-400">
                        <div class="flex items-center">
                            <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                            Opened: {$date}
                        </div>
                        {if $lastreply}
                            <div class="flex items-center">
                                <i data-lucide="clock" class="w-4 h-4 mr-1"></i>
                                Last Reply: {$lastreply}
                            </div>
                        {/if}
                        <div class="flex items-center">
                            <i data-lucide="folder" class="w-4 h-4 mr-1"></i>
                            {$department}
                        </div>
                    </div>
                </div>
                <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
                    <a href="{$systemurl}/supporttickets.php" class="inline-flex items-center px-4 py-2 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-300">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                        Back to Tickets
                    </a>
                    {if $status neq "Closed"}
                        <a href="?tid={$ticketnum}&c={$c}&closeticket=true" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 text-red-300 hover:text-red-200 hover:bg-gradient-to-r hover:from-red-500/30 hover:to-pink-500/30 rounded-lg transition-all duration-300">
                            <i data-lucide="x-circle" class="w-4 h-4 mr-2"></i>
                            Close Ticket
                        </a>
                    {/if}
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <span class="text-red-300 font-medium">{$errormessage}</span>
                </div>
            </div>
        {/if}

        {if $successmessage}
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-medium">{$successmessage}</span>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Main Ticket Thread -->
            <div class="lg:col-span-3">
                <!-- Ticket Conversation -->
                <div class="space-y-6 mb-8">
                    {foreach from=$replies item=reply}
                        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl
                            {if $reply.admin}border-l-4 border-l-blue-500{else}border-l-4 border-l-purple-500{/if}">
                            
                            <!-- Reply Header -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 
                                        {if $reply.admin}bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-500/30{else}bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30{/if} 
                                        rounded-full flex items-center justify-center">
                                        {if $reply.admin}
                                            <i data-lucide="headphones" class="w-5 h-5 text-blue-400"></i>
                                        {else}
                                            <i data-lucide="user" class="w-5 h-5 text-purple-400"></i>
                                        {/if}
                                    </div>
                                    <div>
                                        <div class="font-semibold text-white">
                                            {if $reply.admin}
                                                {if $reply.name}{$reply.name}{else}Support Team{/if}
                                                <span class="ml-2 px-2 py-0.5 bg-blue-500/20 border border-blue-500/30 rounded-full text-xs text-blue-300">Staff</span>
                                            {else}
                                                {if $reply.name}{$reply.name}{else}You{/if}
                                                <span class="ml-2 px-2 py-0.5 bg-purple-500/20 border border-purple-500/30 rounded-full text-xs text-purple-300">Customer</span>
                                            {/if}
                                        </div>
                                        <div class="text-sm text-gray-400 flex items-center">
                                            <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                                            {$reply.date}
                                        </div>
                                    </div>
                                </div>
                                
                                {if $reply.rating}
                                    <div class="flex items-center space-x-1">
                                        {for $i=1 to 5}
                                            {if $i <= $reply.rating}
                                                <i data-lucide="star" class="w-4 h-4 text-yellow-400 fill-current"></i>
                                            {else}
                                                <i data-lucide="star" class="w-4 h-4 text-gray-600"></i>
                                            {/if}
                                        {/for}
                                    </div>
                                {/if}
                            </div>

                            <!-- Reply Content -->
                            <div class="prose prose-invert max-w-none">
                                <div class="text-gray-300 leading-relaxed">
                                    {$reply.message}
                                </div>
                            </div>

                            <!-- Attachments -->
                            {if $reply.attachments}
                                <div class="mt-4 pt-4 border-t border-slate-700/50">
                                    <h4 class="text-sm font-semibold text-gray-300 mb-3 flex items-center">
                                        <i data-lucide="paperclip" class="w-4 h-4 mr-2"></i>
                                        Attachments
                                    </h4>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                        {foreach from=$reply.attachments item=attachment}
                                            <a href="{$attachment.downloadlink}" 
                                               class="flex items-center p-3 bg-slate-800/50 border border-slate-600/50 rounded-lg hover:bg-slate-700/50 transition-all duration-300 group">
                                                <div class="w-8 h-8 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center mr-3">
                                                    {if $attachment.type eq "image"}
                                                        <i data-lucide="image" class="w-4 h-4 text-emerald-400"></i>
                                                    {elseif $attachment.type eq "pdf"}
                                                        <i data-lucide="file-text" class="w-4 h-4 text-red-400"></i>
                                                    {elseif $attachment.type eq "archive"}
                                                        <i data-lucide="archive" class="w-4 h-4 text-purple-400"></i>
                                                    {else}
                                                        <i data-lucide="file" class="w-4 h-4 text-blue-400"></i>
                                                    {/if}
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <div class="font-medium text-white text-sm truncate group-hover:text-blue-300 transition-colors">
                                                        {$attachment.filename}
                                                    </div>
                                                    <div class="text-xs text-gray-400">
                                                        {$attachment.filesize}
                                                    </div>
                                                </div>
                                                <i data-lucide="download" class="w-4 h-4 text-gray-400 group-hover:text-blue-400 transition-colors"></i>
                                            </a>
                                        {/foreach}
                                    </div>
                                </div>
                            {/if}
                        </div>
                    {/foreach}
                </div>

                <!-- Reply Form -->
                {if $status neq "Closed"}
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="message-square" class="w-5 h-5 mr-2 text-blue-400"></i>
                            Add Reply
                        </h3>
                        
                        <form method="post" action="{$smarty.server.PHP_SELF}?tid={$ticketnum}&c={$c}" enctype="multipart/form-data" class="space-y-6">
                            <div class="space-y-1">
                                <label for="message" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="edit-3" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                    Your Reply <span class="text-red-400">*</span>
                                </label>
                                <textarea name="message" 
                                          id="message" 
                                          rows="6" 
                                          class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300 resize-y"
                                          placeholder="Please provide any additional information or updates regarding your issue..."
                                          required></textarea>
                                <div class="text-sm text-gray-400 flex items-center">
                                    <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                                    Be clear and detailed to help our support team assist you effectively.
                                </div>
                            </div>

                            <!-- File Attachments -->
                            <div class="space-y-3">
                                <label class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="paperclip" class="w-4 h-4 mr-2 inline text-teal-400"></i>
                                    Attachments (Optional)
                                </label>
                                <div class="space-y-2">
                                    {for $i=0 to 2}
                                        <input type="file" 
                                               name="attachments[]" 
                                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300 hover:file:bg-blue-500/30 transition-all duration-300"
                                               accept=".jpg,.jpeg,.png,.gif,.pdf,.txt,.doc,.docx,.zip,.log">
                                    {/for}
                                </div>
                                <div class="text-sm text-gray-400 flex items-center">
                                    <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                                    Max file size: 10MB per file. Supported: Images, documents, logs, archives.
                                </div>
                            </div>

                            <div class="flex flex-col sm:flex-row gap-4 pt-4 border-t border-slate-700/50">
                                <button type="submit" 
                                        name="submitupdate"
                                        class="flex-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                                    <i data-lucide="send" class="w-4 h-4 mr-2 inline"></i>
                                    Send Reply
                                </button>
                                <a href="{$systemurl}/supporttickets.php" 
                                   class="flex-1 text-center bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-6 py-3 rounded-lg font-medium transition-all duration-300">
                                    <i data-lucide="x" class="w-4 h-4 mr-2 inline"></i>
                                    Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                {else}
                    <!-- Closed Ticket Notice -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-slate-500/20 to-gray-500/20 border border-slate-500/30 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="lock" class="w-8 h-8 text-slate-400"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-white mb-2">Ticket Closed</h3>
                            <p class="text-gray-400 mb-4">This support ticket has been closed and is no longer accepting replies.</p>
                            <a href="{$systemurl}/submitticket.php" 
                               class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02]">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Open New Ticket
                            </a>
                        </div>
                    </div>
                {/if}
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Ticket Information -->
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="info" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Ticket Details
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400 text-sm">Ticket ID:</span>
                            <span class="font-medium text-white">#{$ticketnum}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400 text-sm">Status:</span>
                            <span class="font-medium 
                                {if $status eq "Open"}text-emerald-400
                                {elseif $status eq "Answered"}text-blue-400
                                {elseif $status eq "Customer-Reply"}text-orange-400
                                {elseif $status eq "Closed"}text-slate-400
                                {else}text-purple-400{/if}">
                                {$status}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400 text-sm">Priority:</span>
                            <span class="font-medium 
                                {if $urgency eq "High"}text-red-400
                                {elseif $urgency eq "Medium"}text-yellow-400
                                {else}text-gray-400{/if}">
                                {if $urgency}{$urgency}{else}Normal{/if}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400 text-sm">Department:</span>
                            <span class="font-medium text-white">{$department}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400 text-sm">Created:</span>
                            <span class="font-medium text-white">{$date}</span>
                        </div>
                        {if $lastreply}
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400 text-sm">Last Reply:</span>
                                <span class="font-medium text-white">{$lastreply}</span>
                            </div>
                        {/if}
                    </div>
                </div>

                {if $relatedservice}
                    <!-- Related Service -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="server" class="w-5 h-5 mr-2 text-emerald-400"></i>
                            Related Service
                        </h3>
                        <div class="space-y-3">
                            <div>
                                <div class="font-medium text-white">{$relatedservice.productname}</div>
                                <div class="text-sm text-gray-400">{$relatedservice.domain}</div>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-400 text-sm">Status:</span>
                                <span class="font-medium text-emerald-400">{$relatedservice.status}</span>
                            </div>
                            <a href="clientarea.php?action=productdetails&id={$relatedservice.id}" 
                               class="w-full bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg text-center transition-all duration-300">
                                <i data-lucide="external-link" class="w-4 h-4 mr-2 inline"></i>
                                View Service
                            </a>
                        </div>
                    </div>
                {/if}

                <!-- Helpful Resources -->
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="help-circle" class="w-5 h-5 mr-2 text-yellow-400"></i>
                        Need More Help?
                    </h3>
                    <div class="space-y-3">
                        <a href="{$systemurl}/knowledgebase.php" 
                           class="w-full bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-all duration-300 flex items-center">
                            <i data-lucide="book-open" class="w-4 h-4 mr-3 text-blue-400"></i>
                            Knowledge Base
                        </a>
                        <a href="{$systemurl}/submitticket.php" 
                           class="w-full bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-all duration-300 flex items-center">
                            <i data-lucide="plus-circle" class="w-4 h-4 mr-3 text-emerald-400"></i>
                            New Ticket
                        </a>
                        <a href="{$systemurl}/serverstatus.php" 
                           class="w-full bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-all duration-300 flex items-center">
                            <i data-lucide="activity" class="w-4 h-4 mr-3 text-purple-400"></i>
                            System Status
                        </a>
                    </div>
                </div>

                <!-- Rating Request -->
                {if $status eq "Answered" or $status eq "Closed"}
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="star" class="w-5 h-5 mr-2 text-yellow-400"></i>
                            Rate Our Support
                        </h3>
                        <p class="text-gray-400 text-sm mb-4">How would you rate the support you received?</p>
                        <div class="flex justify-center space-x-2 mb-4">
                            {for $i=1 to 5}
                                <button type="button" 
                                        class="w-8 h-8 text-yellow-400 hover:text-yellow-300 transition-colors cursor-pointer rating-star" 
                                        data-rating="{$i}">
                                    <i data-lucide="star" class="w-6 h-6"></i>
                                </button>
                            {/for}
                        </div>
                        <button type="button" 
                                class="w-full bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 text-yellow-300 hover:text-yellow-200 px-4 py-2 rounded-lg transition-all duration-300">
                            Submit Rating
                        </button>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Star rating functionality
    const stars = document.querySelectorAll('.rating-star');
    let currentRating = 0;
    
    stars.forEach((star, index) => {
        star.addEventListener('mouseenter', function() {
            highlightStars(index + 1);
        });
        
        star.addEventListener('mouseleave', function() {
            highlightStars(currentRating);
        });
        
        star.addEventListener('click', function() {
            currentRating = index + 1;
            highlightStars(currentRating);
            // Here you would typically send the rating to the server
            console.log('Rating selected:', currentRating);
        });
    });
    
    function highlightStars(rating) {
        stars.forEach((star, index) => {
            const icon = star.querySelector('i');
            if (index < rating) {
                icon.classList.add('fill-current');
                icon.classList.remove('text-yellow-400');
                icon.classList.add('text-yellow-300');
            } else {
                icon.classList.remove('fill-current');
                icon.classList.remove('text-yellow-300');
                icon.classList.add('text-yellow-400');
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const message = document.getElementById('message').value.trim();
            
            if (message.length < 10) {
                e.preventDefault();
                alert('Please provide more details in your reply (minimum 10 characters).');
                return false;
            }
        });
    }
});
</script>

{include file="$template/footer.tpl"}