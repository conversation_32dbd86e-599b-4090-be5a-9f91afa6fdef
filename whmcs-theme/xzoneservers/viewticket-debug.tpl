{include file="$template/header.tpl"}

<div class="container py-8">
    <h1>Debug: View Ticket</h1>
    
    <div class="debug-info bg-slate-800 p-4 rounded mb-4">
        <h3>Available Variables:</h3>
        <p>Ticket Number: {$ticketnum}</p>
        <p>Ticket ID: {$tid}</p>
        <p>Status: {$status}</p>
        <p>Subject: {$subject}</p>
        <p>Date: {$date}</p>
        <p>Department: {$department}</p>
        <p>Priority: {$urgency}</p>
        <p>Client Name: {$name}</p>
        <p>Client Email: {$email}</p>
    </div>

    {if $subject}
        <div class="ticket-content bg-slate-900 p-6 rounded">
            <h2 class="text-2xl font-bold text-white mb-4">Ticket #{$ticketnum}: {$subject}</h2>
            
            <div class="ticket-info mb-4">
                <p class="text-gray-300">Status: <span class="text-blue-400">{$status}</span></p>
                <p class="text-gray-300">Date: {$date}</p>
                <p class="text-gray-300">Department: {$department}</p>
                <p class="text-gray-300">Priority: {$urgency}</p>
            </div>

            {if $replies}
                <div class="replies">
                    <h3 class="text-xl font-semibold text-white mb-4">Ticket Replies:</h3>
                    {foreach from=$replies item=reply}
                        <div class="reply mb-4 p-4 bg-slate-800 rounded">
                            <div class="reply-header mb-2">
                                <strong class="text-white">{$reply.name}</strong>
                                <span class="text-gray-400 text-sm">({$reply.date})</span>
                            </div>
                            <div class="reply-message text-gray-300">
                                {$reply.message}
                            </div>
                        </div>
                    {/foreach}
                </div>
            {else}
                <p class="text-gray-400">No replies found.</p>
            {/if}

            {if $status neq "Closed"}
                <div class="reply-form mt-6">
                    <h3 class="text-xl font-semibold text-white mb-4">Reply to Ticket</h3>
                    <form method="post" action="viewticket.php?tid={$ticketnum}&c={$c}">
                        <textarea name="message" rows="6" class="w-full p-3 bg-slate-800 border border-slate-600 rounded text-white" placeholder="Enter your reply..."></textarea>
                        <button type="submit" name="addreply" class="mt-3 px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            Add Reply
                        </button>
                    </form>
                </div>
            {/if}
        </div>
    {else}
        <div class="error bg-red-500/10 border border-red-500/30 rounded p-4">
            <p class="text-red-300">No ticket data found. This could indicate:</p>
            <ul class="text-red-300 mt-2 ml-4">
                <li>• The ticket doesn't exist</li>
                <li>• You don't have permission to view this ticket</li>
                <li>• There's an issue with the WHMCS template variables</li>
            </ul>
        </div>
    {/if}

    <div class="actions mt-6">
        <a href="supporttickets.php" class="px-4 py-2 bg-slate-600 text-white rounded hover:bg-slate-700">
            ← Back to Tickets
        </a>
    </div>
</div>

{include file="$template/footer.tpl"}