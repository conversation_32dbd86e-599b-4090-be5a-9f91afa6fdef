{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Welcome Section -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white mb-2">
                        Welcome back, {$clientsdetails.firstname}!
                    </h1>
                    <p class="text-gray-300">
                        Manage your services, view invoices, and get support from your dashboard.
                    </p>
                </div>
                <div class="hidden md:block">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <i data-lucide="user" class="w-8 h-8 text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="server" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$clientsstats.productsnumactive}</h3>
                <p class="text-gray-300 text-sm">Active Services</p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="file-text" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$clientsstats.numunpaidinvoices}</h3>
                <p class="text-gray-300 text-sm">Unpaid Invoices</p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="ticket" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$clientsstats.numactivetickets}</h3>
                <p class="text-gray-300 text-sm">Open Tickets</p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="credit-card" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">
                    {if $clientsstats.creditbalance > 0}
                        {$clientsstats.creditbalance}
                    {else}
                        €0.00
                    {/if}
                </h3>
                <p class="text-gray-300 text-sm">Account Credit</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Services -->
        <div class="panel">
            <div class="panel-heading">
                <h3 class="flex items-center">
                    <i data-lucide="server" class="w-5 h-5 mr-2 text-blue-400"></i>
                    Your Services
                </h3>
            </div>
            <div class="panel-body">
                {if $services}
                    <div class="space-y-4">
                        {foreach from=$services item=service name=services}
                            {if $smarty.foreach.services.index < 5}
                            <div class="flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-slate-700/50 hover:border-blue-500/30 transition-all duration-300">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center mr-4">
                                        <i data-lucide="server" class="w-5 h-5 text-blue-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white">{$service.product}</h4>
                                        <p class="text-sm text-gray-400">{$service.domain}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="badge badge-{$service.status|lower}">
                                        {$service.status}
                                    </span>
                                    <p class="text-sm text-gray-400 mt-1">
                                        Next Due: {$service.nextduedate}
                                    </p>
                                </div>
                            </div>
                            {/if}
                        {/foreach}
                    </div>
                    <div class="mt-6 text-center">
                        <a href="clientarea.php?action=services" class="btn btn-secondary">
                            <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                            View All Services
                        </a>
                    </div>
                {else}
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="server" class="w-8 h-8 text-gray-400"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">No Services Yet</h4>
                        <p class="text-gray-400 mb-4">Get started with our hosting solutions</p>
                        <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Order Now
                        </a>
                    </div>
                {/if}
            </div>
        </div>

        <!-- Recent Invoices -->
        <div class="panel">
            <div class="panel-heading">
                <h3 class="flex items-center">
                    <i data-lucide="file-text" class="w-5 h-5 mr-2 text-emerald-400"></i>
                    Recent Invoices
                </h3>
            </div>
            <div class="panel-body">
                {if $invoices}
                    <div class="space-y-4">
                        {foreach from=$invoices item=invoice name=invoices}
                            {if $smarty.foreach.invoices.index < 5}
                            <div class="flex items-center justify-between p-4 bg-slate-800/30 rounded-lg border border-slate-700/50 hover:border-emerald-500/30 transition-all duration-300">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-lg flex items-center justify-center mr-4">
                                        <i data-lucide="file-text" class="w-5 h-5 text-emerald-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white">Invoice #{$invoice.invoicenum}</h4>
                                        <p class="text-sm text-gray-400">{$invoice.datedue}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-white">{$invoice.total}</p>
                                    <span class="badge badge-{$invoice.status|lower}">
                                        {$invoice.status}
                                    </span>
                                </div>
                            </div>
                            {/if}
                        {/foreach}
                    </div>
                    <div class="mt-6 text-center">
                        <a href="clientarea.php?action=invoices" class="btn btn-secondary">
                            <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                            View All Invoices
                        </a>
                    </div>
                {else}
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i data-lucide="file-text" class="w-8 h-8 text-gray-400"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-white mb-2">No Invoices</h4>
                        <p class="text-gray-400">Your invoices will appear here</p>
                    </div>
                {/if}
            </div>
        </div>
    </div>

    <!-- Support Tickets -->
    <div class="panel mt-8">
        <div class="panel-heading">
            <h3 class="flex items-center">
                <i data-lucide="life-buoy" class="w-5 h-5 mr-2 text-orange-400"></i>
                Recent Support Tickets
            </h3>
        </div>
        <div class="panel-body">
            {if $tickets}
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Subject</th>
                                <th>Department</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$tickets item=ticket name=tickets}
                                {if $smarty.foreach.tickets.index < 5}
                                <tr>
                                    <td>
                                        <span class="font-semibold text-white">#{$ticket.tid}</span>
                                    </td>
                                    <td>
                                        <div class="max-w-xs truncate text-gray-300">
                                            {$ticket.subject}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-gray-400">{$ticket.department}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-{$ticket.status|strip_tags|lower}">
                                            {$ticket.status|strip_tags}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="text-gray-400">{$ticket.lastreply}</span>
                                    </td>
                                    <td>
                                        <a href="supporttickets.php?action=view&id={$ticket.tid}" class="btn btn-sm btn-secondary">
                                            <i data-lucide="eye" class="w-3 h-3 mr-1"></i>
                                            View
                                        </a>
                                    </td>
                                </tr>
                                {/if}
                            {/foreach}
                        </tbody>
                    </table>
                </div>
                <div class="mt-6 text-center">
                    <a href="supporttickets.php" class="btn btn-secondary">
                        <i data-lucide="arrow-right" class="w-4 h-4 mr-2"></i>
                        View All Tickets
                    </a>
                </div>
            {else}
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="life-buoy" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-white mb-2">No Support Tickets</h4>
                    <p class="text-gray-400 mb-4">Need help? Create a support ticket</p>
                    <a href="submitticket.php" class="btn btn-primary">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Open Ticket
                    </a>
                </div>
            {/if}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
        <div class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-700/50 rounded-xl p-6">
            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                <i data-lucide="zap" class="w-5 h-5 mr-2 text-yellow-400"></i>
                Quick Actions
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="clientarea.php?action=services" class="flex items-center p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg hover:bg-blue-500/20 transition-all duration-300">
                    <i data-lucide="server" class="w-6 h-6 text-blue-400 mr-3"></i>
                    <span class="text-white font-medium">My Services</span>
                </a>
                <a href="clientarea.php?action=invoices" class="flex items-center p-4 bg-emerald-500/10 border border-emerald-500/20 rounded-lg hover:bg-emerald-500/20 transition-all duration-300">
                    <i data-lucide="file-text" class="w-6 h-6 text-emerald-400 mr-3"></i>
                    <span class="text-white font-medium">Invoices</span>
                </a>
                <a href="submitticket.php" class="flex items-center p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg hover:bg-orange-500/20 transition-all duration-300">
                    <i data-lucide="life-buoy" class="w-6 h-6 text-orange-400 mr-3"></i>
                    <span class="text-white font-medium">Get Support</span>
                </a>
                <a href="{$WEB_ROOT}/cart.php" class="flex items-center p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg hover:bg-purple-500/20 transition-all duration-300">
                    <i data-lucide="shopping-cart" class="w-6 h-6 text-purple-400 mr-3"></i>
                    <span class="text-white font-medium">Order More</span>
                </a>
            </div>
        </div>
    </div>
</div>

{include file="$template/footer.tpl"}
