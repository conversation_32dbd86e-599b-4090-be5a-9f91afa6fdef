{include file="$template/header.tpl"}

<!-- Department Selection Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="plus-circle" class="w-3 h-3 mr-1.5"></i>
                    New Support Request
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="clock" class="w-3 h-3 mr-1.5"></i>
                    24/7 Support
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Choose Support Department
                </span>
            </h1>
            <p class="text-lg text-gray-300">{$LANG.supportticketsheader}</p>
        </div>

        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 shadow-2xl">
            <div class="mb-6">
                <h2 class="text-xl font-semibold text-white mb-2 flex items-center">
                    <i data-lucide="folder" class="w-5 h-5 mr-2 text-blue-400"></i>
                    Select the Most Appropriate Department
                </h2>
                <p class="text-gray-400 text-sm">Choose the department that best matches your inquiry for the fastest response.</p>
            </div>

            {if $departments}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {foreach from=$departments key=num item=department}
                        <a href="{$smarty.server.PHP_SELF}?step=2&deptid={$department.id}" 
                           class="group bg-slate-800/50 border border-slate-600/50 rounded-xl p-6 hover:bg-slate-700/50 hover:border-slate-500/50 transition-all duration-300 transform hover:scale-[1.02]">
                            <div class="flex items-start">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center mr-4">
                                    <i data-lucide="folder" class="w-6 h-6 text-blue-400 group-hover:text-blue-300"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-white group-hover:text-blue-300 transition-colors mb-2">
                                        {$department.name}
                                    </h3>
                                    {if $department.description}
                                        <p class="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                                            {$department.description}
                                        </p>
                                    {else}
                                        <p class="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                                            Get help with {$department.name|lower} related questions.
                                        </p>
                                    {/if}
                                </div>
                                <div class="ml-2">
                                    <i data-lucide="arrow-right" class="w-5 h-5 text-gray-400 group-hover:text-blue-400 transition-colors"></i>
                                </div>
                            </div>
                        </a>
                    {/foreach}
                </div>
            {else}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-slate-800/50 border border-slate-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="alert-circle" class="w-8 h-8 text-orange-400"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-2">No Support Departments Available</h3>
                    <p class="text-gray-400">{$LANG.nosupportdepartments}</p>
                </div>
            {/if}
        </div>

        <!-- Additional Information -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Support Hours -->
            <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <i data-lucide="clock" class="w-5 h-5 mr-2 text-emerald-400"></i>
                    Support Hours
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-300">Critical Issues</span>
                        <span class="text-emerald-400 font-semibold">24/7</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-300">General Support</span>
                        <span class="text-blue-400 font-semibold">Business Hours</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-300">Average Response</span>
                        <span class="text-purple-400 font-semibold">< 15 minutes</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <i data-lucide="zap" class="w-5 h-5 mr-2 text-yellow-400"></i>
                    Quick Actions
                </h3>
                <div class="space-y-3">
                    <a href="supporttickets.php" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                        <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                        View Existing Tickets
                    </a>
                    <a href="knowledgebase.php" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors">
                        <i data-lucide="book" class="w-4 h-4 mr-2"></i>
                        Browse Knowledge Base
                    </a>
                    <a href="serverstatus.php" class="flex items-center text-purple-400 hover:text-purple-300 transition-colors">
                        <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                        Check System Status
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

{include file="$template/footer.tpl"}