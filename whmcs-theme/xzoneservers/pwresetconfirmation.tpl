{include file="$template/header.tpl"}

<!-- Compact Password Reset Confirmation Section -->
<section class="py-6 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md mx-auto">
        <!-- Compact <PERSON> Badges -->
        <div class="flex items-center justify-center gap-3 mb-4">
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="check-circle" class="w-3 h-3 mr-1.5"></i>
                Success
            </div>
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                Secure
            </div>
        </div>

        <!-- Page Title -->
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-white mb-2">
                <i data-lucide="check-circle-2" class="w-6 h-6 mr-2 inline text-green-400"></i>
                Password Reset Complete
            </h1>
            <p class="text-gray-400 text-sm">
                Your password has been successfully updated
            </p>
        </div>

        <!-- Compact Password Reset Confirmation Card -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <!-- Success Message -->
            <div class="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30 rounded-xl p-6 mb-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="check" class="w-8 h-8 text-white"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-green-300 mb-2">Password Updated Successfully!</h3>
                    <p class="text-green-400 text-sm">
                        Your account password has been changed. You can now login with your new password.
                    </p>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <i data-lucide="info" class="w-5 h-5 mr-3 text-blue-400 mt-0.5 flex-shrink-0"></i>
                    <div>
                        <h4 class="text-blue-300 font-medium mb-1">Security Notice</h4>
                        <p class="text-blue-400 text-sm">
                            For your security, you have been logged out of all devices. Please login again with your new password.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <a href="login.php" 
                   class="w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 shadow-lg hover:shadow-blue-500/25">
                    <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                    Login to Your Account
                </a>
                
                <a href="clientarea.php" 
                   class="w-full inline-flex items-center justify-center px-4 py-3 bg-slate-800/50 border border-slate-600/50 text-gray-300 font-medium rounded-lg hover:bg-slate-700/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-300">
                    <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                    Go to Client Area
                </a>
            </div>

            <!-- Additional Security Tips -->
            <div class="mt-6 p-4 bg-slate-800/30 rounded-lg border border-slate-600/30">
                <h4 class="text-white font-medium mb-2 flex items-center">
                    <i data-lucide="shield" class="w-4 h-4 mr-2 text-yellow-400"></i>
                    Security Tips
                </h4>
                <ul class="text-sm text-gray-400 space-y-1">
                    <li class="flex items-start">
                        <i data-lucide="check" class="w-3 h-3 mr-2 text-green-400 mt-0.5 flex-shrink-0"></i>
                        Use a unique password for your account
                    </li>
                    <li class="flex items-start">
                        <i data-lucide="check" class="w-3 h-3 mr-2 text-green-400 mt-0.5 flex-shrink-0"></i>
                        Enable two-factor authentication if available
                    </li>
                    <li class="flex items-start">
                        <i data-lucide="check" class="w-3 h-3 mr-2 text-green-400 mt-0.5 flex-shrink-0"></i>
                        Keep your password secure and don't share it
                    </li>
                </ul>
            </div>
        </div>

        <!-- Compact Footer -->
        <div class="mt-4 text-center">
            <p class="text-gray-400 text-sm mb-2">
                Need help?
                <a href="contact.php" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Contact Support
                </a>
            </p>
            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Auto-redirect to login after 10 seconds (optional)
    let countdown = 10;
    const loginButton = document.querySelector('a[href="login.php"]');
    
    if (loginButton) {
        const originalText = loginButton.innerHTML;
        
        const updateCountdown = () => {
            if (countdown > 0) {
                loginButton.innerHTML = `<i data-lucide="log-in" class="w-4 h-4 mr-2"></i>Login to Your Account (${countdown}s)`;
                countdown--;
                setTimeout(updateCountdown, 1000);
            } else {
                // Redirect to login
                window.location.href = 'login.php';
            }
        };
        
        // Start countdown after 3 seconds
        setTimeout(() => {
            updateCountdown();
        }, 3000);
        
        // Stop countdown if user interacts with the page
        document.addEventListener('click', () => {
            countdown = 0;
            loginButton.innerHTML = originalText;
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    }
});
</script>

{include file="$template/footer.tpl"}
