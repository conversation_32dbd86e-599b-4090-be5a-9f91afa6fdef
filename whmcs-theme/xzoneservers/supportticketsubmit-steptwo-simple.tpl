{include file="$template/header.tpl"}

{if $errormessage}
    <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
        <div class="flex items-center">
            <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
            <div class="text-red-300 font-medium">{$errormessage}</div>
        </div>
    </div>
{/if}

<div class="max-w-4xl mx-auto py-8 px-4">
    <h1 class="text-3xl font-bold text-white mb-6">Submit Support Ticket</h1>
    
    <form method="post" action="{$smarty.server.PHP_SELF}?step=3" enctype="multipart/form-data" role="form">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label for="inputName" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsclientname}</label>
                <input type="text" name="name" id="inputName" value="{$name}" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white{if $loggedin} opacity-50{/if}"{if $loggedin} disabled="disabled"{/if} />
            </div>
            <div>
                <label for="inputEmail" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsclientemail}</label>
                <input type="email" name="email" id="inputEmail" value="{$email}" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white{if $loggedin} opacity-50{/if}"{if $loggedin} disabled="disabled"{/if} />
            </div>
        </div>
        
        <div class="mb-4">
            <label for="inputSubject" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsticketsubject}</label>
            <input type="text" name="subject" id="inputSubject" value="{$subject}" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white" />
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
                <label for="inputDepartment" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsdepartment}</label>
                <select name="deptid" id="inputDepartment" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white" onchange="refreshCustomFields(this)">
                    {foreach from=$departments item=department}
                        <option value="{$department.id}"{if $department.id eq $deptid} selected="selected"{/if}>
                            {$department.name}
                        </option>
                    {/foreach}
                </select>
            </div>
            
            {if $relatedservices}
                <div>
                    <label for="inputRelatedService" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.relatedservice}</label>
                    <select name="relatedservice" id="inputRelatedService" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white">
                        <option value="">{$LANG.none}</option>
                        {foreach from=$relatedservices item=relatedservice}
                            <option value="{$relatedservice.id}"{if $relatedservice.id eq $selectedservice} selected="selected"{/if}>
                                {$relatedservice.name} ({$relatedservice.status})
                            </option>
                        {/foreach}
                    </select>
                </div>
            {/if}
            
            <div>
                <label for="inputPriority" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketspriority}</label>
                <select name="urgency" id="inputPriority" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white">
                    <option value="High"{if $urgency eq "High"} selected="selected"{/if}>
                        {$LANG.supportticketsticketurgencyhigh}
                    </option>
                    <option value="Medium"{if $urgency eq "Medium" || !$urgency} selected="selected"{/if}>
                        {$LANG.supportticketsticketurgencymedium}
                    </option>
                    <option value="Low"{if $urgency eq "Low"} selected="selected"{/if}>
                        {$LANG.supportticketsticketurgencylow}
                    </option>
                </select>
            </div>
        </div>
        
        <div class="mb-4">
            <label for="inputMessage" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.contactmessage}</label>
            <textarea name="message" id="inputMessage" rows="12" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white markdown-editor" data-auto-save-name="client_ticket_open">{$message}</textarea>
        </div>

        <div class="mb-4">
            <label for="inputAttachments" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsticketattachments}</label>
            <input type="file" name="attachments[]" id="inputAttachments" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300" />
            <div id="fileUploadsContainer"></div>
            <button type="button" class="mt-2 px-4 py-2 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg text-sm" onclick="extraTicketAttachment()">
                Add More Files
            </button>
            <div class="text-sm text-gray-400 mt-2">
                {$LANG.supportticketsallowedextensions}: {$allowedfiletypes} ({lang key="maxFileSize" fileSize="$uploadMaxFileSize"})
            </div>
        </div>

        <div id="customFieldsContainer">
            {include file="$template/supportticketsubmit-customfields.tpl"}
        </div>

        <div id="autoAnswerSuggestions" class="hidden"></div>

        <div class="text-center mb-4">
            {include file="$template/includes/captcha.tpl"}
        </div>

        <div class="text-center">
            <input type="submit" id="openTicketSubmit" value="{$LANG.supportticketsticketsubmit}" class="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 disable-on-click{if $captcha}{$captcha->getButtonClass($captchaForm)}{/if}" />
            <a href="supporttickets.php" class="ml-4 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-8 py-3 rounded-lg font-medium transition-all duration-300">Cancel</a>
        </div>

    </form>
</div>

<script>
function extraTicketAttachment() {
    const container = document.getElementById('fileUploadsContainer');
    const input = document.createElement('input');
    input.type = 'file';
    input.name = 'attachments[]';
    input.className = 'w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300 mt-2';
    container.appendChild(input);
}

function refreshCustomFields(select) {
    // This would typically make an AJAX call to refresh custom fields
    console.log('Department changed to:', select.value);
}
</script>

{if $kbsuggestions}
    <script>
        jQuery(document).ready(function() {
            getTicketSuggestions();
        });
    </script>
{/if}

{include file="$template/footer.tpl"}