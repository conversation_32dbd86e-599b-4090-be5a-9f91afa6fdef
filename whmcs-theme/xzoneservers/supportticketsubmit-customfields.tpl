{if $customfields}
    <div class="border-t border-slate-700/50 pt-6">
        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
            <i data-lucide="settings" class="w-5 h-5 mr-2 text-orange-400"></i>
            Additional Information
        </h3>
        <div class="space-y-4">
            {foreach from=$customfields item=customfield}
                <div class="space-y-1">
                    <label for="customfield{$customfield.id}" class="block text-sm font-medium text-gray-300">
                        <i data-lucide="edit-3" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                        {$customfield.name}
                        {if $customfield.required}<span class="text-red-400">*</span>{/if}
                    </label>
                    
                    <div class="custom-field-input">
                        {* Apply styling to the custom field input *}
                        {assign var="styledInput" value=$customfield.input}
                        {$styledInput|regex_replace:'/class="([^"]*)"/':"class=\"$1 w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300\""}
                    </div>
                    
                    {if $customfield.description}
                        <div class="text-sm text-gray-400 flex items-center mt-1">
                            <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                            {$customfield.description}
                        </div>
                    {/if}
                </div>
            {/foreach}
        </div>
    </div>
{/if}