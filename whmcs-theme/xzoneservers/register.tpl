{include file="$template/header.tpl"}

<!-- Compact Registration Section -->
<section class="py-6 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-2xl mx-auto">
        <!-- Compact <PERSON> -->
        <div class="flex items-center justify-center gap-3 mb-4">
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="user-plus" class="w-3 h-3 mr-1.5"></i>
                New Account
            </div>
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="zap" class="w-3 h-3 mr-1.5"></i>
                Instant Setup
            </div>
        </div>

        <!-- Page Title -->
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-white mb-2">
                <i data-lucide="user-plus" class="w-6 h-6 mr-2 inline text-blue-400"></i>
                Create Your Account
            </h1>
            <p class="text-gray-400 text-sm">
                Join thousands of satisfied customers and get started with enterprise-grade hosting
            </p>
        </div>

        <!-- Compact Registration Card -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <!-- Alert Messages -->
            {if $errormessage}
                <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                        <span class="text-red-300 font-medium">{$errormessage}</span>
                    </div>
                </div>
            {/if}

            <form method="post" action="{$smarty.server.PHP_SELF}" class="space-y-6 registration-form" role="form">
                <!-- Personal Information Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <i data-lucide="user" class="w-5 h-5 mr-2 text-blue-400"></i>
                        <h3 class="text-lg font-semibold text-white">Personal Information</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-1">
                            <label for="inputFirstName" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="user" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                {$LANG.clientareafirstname} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="firstname"
                                   id="inputFirstName"
                                   value="{$clientsdetails.firstname}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                   placeholder="Enter your first name"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputLastName" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="user" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                {$LANG.clientarealastname} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="lastname"
                                   id="inputLastName"
                                   value="{$clientsdetails.lastname}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                   placeholder="Enter your last name"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputCompanyName" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="building" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                {$LANG.clientareacompanyname}
                            </label>
                            <input type="text"
                                   name="companyname"
                                   id="inputCompanyName"
                                   value="{$clientsdetails.companyname}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                   placeholder="Company name (optional)">
                        </div>

                        <div class="space-y-1">
                            <label for="inputEmail" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                {$LANG.clientareaemail} <span class="text-red-400">*</span>
                            </label>
                            <input type="email"
                                   name="email"
                                   id="inputEmail"
                                   value="{$clientsdetails.email}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                   placeholder="Enter your email address"
                                   required>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-purple-400"></i>
                        <h3 class="text-lg font-semibold text-white">Contact Information</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-1 md:col-span-2">
                            <label for="inputAddress1" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="map-pin" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareaaddress1} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="address1"
                                   id="inputAddress1"
                                   value="{$clientsdetails.address1}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Enter your street address"
                                   required>
                        </div>

                        <div class="space-y-1 md:col-span-2">
                            <label for="inputAddress2" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="map-pin" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareaaddress2}
                            </label>
                            <input type="text"
                                   name="address2"
                                   id="inputAddress2"
                                   value="{$clientsdetails.address2}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Apartment, suite, etc. (optional)">
                        </div>

                        <div class="space-y-1">
                            <label for="inputCity" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="map" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareacity} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="city"
                                   id="inputCity"
                                   value="{$clientsdetails.city}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Enter your city"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputState" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="map" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareastate} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="state"
                                   id="inputState"
                                   value="{$clientsdetails.state}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Enter your state/province"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputPostcode" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="hash" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareapostcode} <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   name="postcode"
                                   id="inputPostcode"
                                   value="{$clientsdetails.postcode}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Enter your postal code"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputCountry" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="globe" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareacountry} <span class="text-red-400">*</span>
                            </label>
                            <select name="country" id="inputCountry"
                                    class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                    required>
                                <option value="">{$LANG.choosecountry}</option>
                                {foreach from=$countries key=code item=countryname}
                                    <option value="{$code}"{if $clientsdetails.country eq $code} selected{/if}>
                                        {$countryname}
                                    </option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="space-y-1">
                            <label for="inputPhone" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="phone" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                {$LANG.clientareaphonenumber} <span class="text-red-400">*</span>
                            </label>
                            <input type="tel"
                                   name="phonenumber"
                                   id="inputPhone"
                                   value="{$clientsdetails.phonenumber}"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                   placeholder="Enter your phone number"
                                   required>
                        </div>
                    </div>
                </div>

                <!-- Account Security Section -->
                <div class="space-y-4">
                    <div class="flex items-center mb-4">
                        <i data-lucide="shield" class="w-5 h-5 mr-2 text-green-400"></i>
                        <h3 class="text-lg font-semibold text-white">Account Security</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-1">
                            <label for="inputNewPassword1" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="lock" class="w-4 h-4 mr-2 inline text-green-400"></i>
                                {$LANG.clientareapassword} <span class="text-red-400">*</span>
                            </label>
                            <input type="password"
                                   name="password"
                                   id="inputNewPassword1"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/50 transition-all duration-300"
                                   placeholder="Enter your password"
                                   required>
                            <div class="text-xs text-gray-400 mt-1">
                                Minimum 8 characters with uppercase, lowercase, and numbers
                            </div>
                        </div>

                        <div class="space-y-1">
                            <label for="inputNewPassword2" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="lock-keyhole" class="w-4 h-4 mr-2 inline text-green-400"></i>
                                {$LANG.clientareaconfirmpassword} <span class="text-red-400">*</span>
                            </label>
                            <input type="password"
                                   name="password2"
                                   id="inputNewPassword2"
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/50 transition-all duration-300"
                                   placeholder="Confirm your password"
                                   required>
                        </div>
                    </div>
                </div>

                {if $customfields}
                    <!-- Additional Information Section -->
                    <div class="space-y-4">
                        <div class="flex items-center mb-4">
                            <i data-lucide="settings" class="w-5 h-5 mr-2 text-orange-400"></i>
                            <h3 class="text-lg font-semibold text-white">Additional Information</h3>
                        </div>
                        {include file="$template/includes/customfields.tpl" customfields=$customfields}
                    </div>
                {/if}

                {if $captcha}
                    <!-- Security Verification -->
                    <div class="space-y-1">
                        <label class="block text-sm font-medium text-gray-300">
                            <i data-lucide="shield-check" class="w-4 h-4 mr-2 inline text-orange-400"></i>
                            Security Verification
                        </label>
                        <div class="captcha-container bg-slate-800/30 p-3 rounded-lg border border-slate-600/50">
                            {$captcha}
                        </div>
                    </div>
                {/if}

                <!-- Terms and Conditions -->
                <div class="space-y-4">
                    <label class="flex items-start cursor-pointer">
                        <input type="checkbox" name="accepttos" class="w-4 h-4 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2 mt-1 mr-3" required>
                        <span class="text-sm text-gray-300">
                            I agree to the
                            <a href="{$WEB_ROOT}/terms.php" target="_blank" class="text-blue-400 hover:text-blue-300 underline">
                                Terms of Service
                            </a>
                            and
                            <a href="{$WEB_ROOT}/privacy.php" target="_blank" class="text-blue-400 hover:text-blue-300 underline">
                                Privacy Policy
                            </a>
                        </span>
                    </label>
                </div>

                <!-- Submit Buttons -->
                <div class="space-y-3">
                    <button type="submit"
                            class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 shadow-lg hover:shadow-blue-500/25">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.orderForm.createAccount}
                    </button>

                    <a href="login.php"
                       class="w-full inline-flex items-center justify-center px-4 py-3 bg-slate-800/50 border border-slate-600/50 text-gray-300 font-medium rounded-lg hover:bg-slate-700/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-slate-500/50 transition-all duration-300">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        {$LANG.existingcustomer}
                    </a>
                </div>
            </form>
        </div>

        <!-- Compact Footer -->
        <div class="mt-4 text-center">
            <p class="text-gray-400 text-sm mb-2">
                Already have an account?
                <a href="login.php" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Sign in here
                </a>
            </p>
            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>

        <!-- Trust Indicators - Compact Version -->
        <div class="mt-6 grid grid-cols-3 gap-3">
            <div class="text-center p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg">
                <i data-lucide="shield-check" class="w-6 h-6 text-green-400 mx-auto mb-1"></i>
                <h4 class="font-medium text-white text-xs mb-1">Secure</h4>
                <p class="text-xs text-gray-400">Enterprise-grade security</p>
            </div>
            <div class="text-center p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-lg">
                <i data-lucide="zap" class="w-6 h-6 text-yellow-400 mx-auto mb-1"></i>
                <h4 class="font-medium text-white text-xs mb-1">Instant</h4>
                <p class="text-xs text-gray-400">Immediate setup</p>
            </div>
            <div class="text-center p-3 bg-gradient-to-r from-pink-500/10 to-red-500/10 border border-pink-500/20 rounded-lg">
                <i data-lucide="headphones" class="w-6 h-6 text-blue-400 mx-auto mb-1"></i>
                <h4 class="font-medium text-white text-xs mb-1">24/7</h4>
                <p class="text-xs text-gray-400">Expert support</p>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Form validation
    const form = document.querySelector('.registration-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const password = document.getElementById('inputNewPassword1').value;
            const confirmPassword = document.getElementById('inputNewPassword2').value;
            const email = document.getElementById('inputEmail').value;
            const acceptTos = document.querySelector('input[name="accepttos"]').checked;

            // Password strength validation
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }

            // Check for uppercase, lowercase, and numbers
            const hasUpper = /[A-Z]/.test(password);
            const hasLower = /[a-z]/.test(password);
            const hasNumber = /\d/.test(password);

            if (!hasUpper || !hasLower || !hasNumber) {
                e.preventDefault();
                alert('Password must contain at least one uppercase letter, one lowercase letter, and one number.');
                return false;
            }

            // Password confirmation validation
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match. Please try again.');
                return false;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }

            // Terms acceptance validation
            if (!acceptTos) {
                e.preventDefault();
                alert('You must accept the Terms of Service and Privacy Policy to continue.');
                return false;
            }
        });

        // Real-time password confirmation validation
        const confirmField = document.getElementById('inputNewPassword2');
        const newPasswordField = document.getElementById('inputNewPassword1');

        if (confirmField && newPasswordField) {
            confirmField.addEventListener('input', function() {
                if (this.value && this.value !== newPasswordField.value) {
                    this.style.borderColor = '#ef4444';
                } else {
                    this.style.borderColor = '';
                }
            });
        }
    }
});
</script>

{include file="$template/footer.tpl"}
