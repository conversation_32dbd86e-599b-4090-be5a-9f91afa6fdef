{if $captcha->isEnabled() && $captcha->isEnabledForForm($captchaForm)}
    <div class="text-center">
        {if $captcha == "recaptcha"}
            <div id="google-recaptcha" class="recaptcha-container"></div>
        {elseif !in_array($captcha, ['invisible', 'recaptcha'])}
            <div class="bg-slate-800/30 p-4 rounded-lg border border-slate-600/50">
                <p class="text-gray-300 mb-3">{lang key="captchaverify"}</p>
                <div class="flex items-center justify-center gap-4">
                    <div class="captchaimage">
                        <img id="inputCaptchaImage" 
                             data-src="{$systemurl}includes/verifyimage.php" 
                             src="{$systemurl}includes/verifyimage.php" 
                             class="border border-slate-600/50 rounded" />
                    </div>
                    <div>
                        <input id="inputCaptcha" 
                               type="text" 
                               name="code" 
                               maxlength="6" 
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                               placeholder="Enter code"
                               data-toggle="tooltip" 
                               data-placement="right" 
                               data-trigger="manual" 
                               title="{lang key='orderForm.required'}"/>
                    </div>
                </div>
            </div>
        {/if}
    </div>
{/if}