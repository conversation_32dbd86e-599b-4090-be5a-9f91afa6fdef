{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div style="background: red; color: white; padding: 20px; margin: 20px;">
        <h1>VIEWINVOICE DEBUG TEST</h1>
        <p>If you can see this, the viewinvoice template is loading.</p>
        <p>Current page: viewinvoice.tpl</p>
        <p>Invoice ID: {$invoiceid}</p>
        <p>Invoice Number: {$invoicenum}</p>
        <p>Status: {$status}</p>
        <p>Invalid Invoice Requested: {if $invalidInvoiceIdRequested}YES{else}NO{/if}</p>
        <p>Date: {$date}</p>
        <p>Total: {$total}</p>
    </div>
    
    {if $invalidInvoiceIdRequested}
        <div style="background: orange; color: black; padding: 20px; margin: 20px;">
            <h2>INVALID INVOICE ID DETECTED</h2>
            <p>The invoice ID requested is invalid or you don't have permission to view it.</p>
        </div>
    {else}
        <div style="background: green; color: white; padding: 20px; margin: 20px;">
            <h2>VALID INVOICE</h2>
            <p>Invoice data is available and should be displayed.</p>
            <p>Invoice Items Count: {if $invoiceitems}{$invoiceitems|count}{else}0{/if}</p>
            <p>Transactions Count: {if $transactions}{$transactions|count}{else}0{/if}</p>
        </div>
    {/if}
</div>

{include file="$template/footer.tpl"}
