{include file="$template/header.tpl"}

<!-- Compact Password Reset Section -->
<section class="py-6 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md mx-auto">
        <!-- Compact <PERSON>ges -->
        <div class="flex items-center justify-center gap-3 mb-4">
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="key" class="w-3 h-3 mr-1.5"></i>
                Password Reset
            </div>
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                Secure Recovery
            </div>
        </div>

        <!-- Page Title -->
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-white mb-2">
                <i data-lucide="lock" class="w-6 h-6 mr-2 inline text-orange-400"></i>
                Reset Your Password
            </h1>
            <p class="text-gray-400 text-sm">
                Enter your email address and we'll send you a link to reset your password
            </p>
        </div>

        <!-- Compact Password Reset Card -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <!-- Alert Messages -->
            {if $pwresetsuccessful}
                <div class="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-green-400"></i>
                        <div>
                            <span class="text-green-300 font-medium block">{$LANG.pwresetsuccessful}</span>
                            <span class="text-green-400 text-sm">Check your email for further instructions</span>
                        </div>
                    </div>
                </div>
            {/if}

            {if $pwreseterror}
                <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                        <span class="text-red-300 font-medium">{$pwreseterror}</span>
                    </div>
                </div>
            {/if}

            {if !$pwresetsuccessful}
                <form method="post" action="pwreset.php" class="space-y-4 pwreset-form" role="form">
                    <div class="space-y-1">
                        <label for="inputEmail" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-orange-400"></i>
                            {$LANG.clientareaemail}
                        </label>
                        <input type="email"
                               name="email"
                               id="inputEmail"
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                               placeholder="{$LANG.enteremail}"
                               autofocus
                               required>
                    </div>

                    {if $captcha}
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-300">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-2 inline text-orange-400"></i>
                                Security Verification
                            </label>
                            <div class="captcha-container bg-slate-800/30 p-3 rounded-lg border border-slate-600/50">
                                {$captcha}
                            </div>
                        </div>
                    {/if}

                    <button type="submit" 
                            class="w-full px-4 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold rounded-lg hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-orange-500/50 transition-all duration-300 shadow-lg hover:shadow-orange-500/25">
                        <i data-lucide="send" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.pwresetsubmit}
                    </button>
                </form>
            {/if}

            <!-- Back to Login Link -->
            <div class="mt-6 text-center">
                <a href="login.php" class="inline-flex items-center text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-1"></i>
                    Back to Login
                </a>
            </div>
        </div>

        <!-- Compact Footer -->
        <div class="mt-4 text-center">
            <p class="text-gray-400 text-sm mb-2">
                Don't have an account?
                <a href="register.php" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                    Create one now
                </a>
            </p>
            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Form validation
    const form = document.querySelector('.pwreset-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const email = document.getElementById('inputEmail').value.trim();
            
            if (!email) {
                e.preventDefault();
                alert('Please enter your email address.');
                return false;
            }
            
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }
        });
    }
});
</script>

{include file="$template/footer.tpl"}
