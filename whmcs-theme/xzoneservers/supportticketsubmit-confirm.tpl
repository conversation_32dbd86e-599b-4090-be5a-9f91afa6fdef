{include file="$template/header.tpl"}

<!-- Ticket Confirmation Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Success Message -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="check-circle" class="w-10 h-10 text-emerald-400"></i>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-emerald-400 via-teal-500 to-cyan-500 bg-clip-text text-transparent">
                    Ticket Created Successfully!
                </span>
            </h1>
            
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-6 mb-6">
                <div class="flex items-center justify-center mb-4">
                    <i data-lucide="ticket" class="w-6 h-6 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-bold text-lg">
                        {$LANG.supportticketsticketcreated}
                        <a href="viewticket.php?tid={$tid}&c={$c}" class="text-emerald-200 hover:text-white transition-colors">
                            #{$tid}
                        </a>
                    </span>
                </div>
                <p class="text-emerald-300/80 text-center">
                    {$LANG.supportticketsticketcreateddesc}
                </p>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 shadow-2xl">
            <h2 class="text-xl font-semibold text-white mb-6 flex items-center">
                <i data-lucide="navigation" class="w-5 h-5 mr-2 text-blue-400"></i>
                What Happens Next?
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="mail" class="w-6 h-6 text-blue-400"></i>
                    </div>
                    <h3 class="font-semibold text-white mb-2">Email Confirmation</h3>
                    <p class="text-sm text-gray-400">You'll receive an email confirmation with your ticket details and tracking information.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="user-check" class="w-6 h-6 text-purple-400"></i>
                    </div>
                    <h3 class="font-semibold text-white mb-2">Expert Review</h3>
                    <p class="text-sm text-gray-400">Our technical experts will review your ticket and begin working on a solution.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="message-circle" class="w-6 h-6 text-emerald-400"></i>
                    </div>
                    <h3 class="font-semibold text-white mb-2">Response & Updates</h3>
                    <p class="text-sm text-gray-400">We'll respond with updates and solutions based on your ticket priority level.</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="viewticket.php?tid={$tid}&c={$c}" 
                   class="flex-1 max-w-xs bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-emerald-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:ring-offset-2 focus:ring-offset-slate-900 text-center">
                    <i data-lucide="eye" class="w-4 h-4 mr-2 inline"></i>
                    View Ticket Details
                </a>
                
                <a href="supporttickets.php" 
                   class="flex-1 max-w-xs bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-6 py-3 rounded-lg font-medium transition-all duration-300 text-center">
                    <i data-lucide="list" class="w-4 h-4 mr-2 inline"></i>
                    All My Tickets
                </a>
                
                <a href="clientarea.php" 
                   class="flex-1 max-w-xs bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-6 py-3 rounded-lg font-medium transition-all duration-300 text-center">
                    <i data-lucide="home" class="w-4 h-4 mr-2 inline"></i>
                    Dashboard
                </a>
            </div>
        </div>

        <!-- Support Information -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Response Times -->
            <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <i data-lucide="clock" class="w-5 h-5 mr-2 text-purple-400"></i>
                    Expected Response Times
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                            <span class="text-gray-300 text-sm">Critical Issues</span>
                        </div>
                        <span class="text-red-400 font-semibold text-sm">< 15 minutes</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-400 rounded-full mr-2"></div>
                            <span class="text-gray-300 text-sm">High Priority</span>
                        </div>
                        <span class="text-orange-400 font-semibold text-sm">< 30 minutes</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                            <span class="text-gray-300 text-sm">General Support</span>
                        </div>
                        <span class="text-blue-400 font-semibold text-sm">< 15 minutes</span>
                    </div>
                </div>
            </div>

            <!-- Additional Resources -->
            <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <i data-lucide="help-circle" class="w-5 h-5 mr-2 text-yellow-400"></i>
                    Additional Resources
                </h3>
                <div class="space-y-3">
                    <a href="knowledgebase.php" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                        <i data-lucide="book" class="w-4 h-4 mr-2"></i>
                        Browse Knowledge Base
                    </a>
                    <a href="serverstatus.php" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors">
                        <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                        System Status Page
                    </a>
                    <a href="contact.php" class="flex items-center text-purple-400 hover:text-purple-300 transition-colors">
                        <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                        Emergency Contact
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

{include file="$template/footer.tpl"}