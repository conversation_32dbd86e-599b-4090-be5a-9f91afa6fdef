{include file="$template/header.tpl"}

<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">My Invoices</h1>
                <p class="text-gray-300">View and manage your billing history</p>
            </div>
            <div class="flex gap-2">
                {if $masspay}
                <button id="mass-pay-btn" class="btn btn-success" onclick="massPayInvoices()">
                    <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                    Pay Selected
                </button>
                {/if}
                <a href="clientarea.php?action=addfunds" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Add Funds
                </a>
            </div>
        </div>
    </div>

    <!-- Invoice Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$numpaidinvoices}</h3>
                <p class="text-gray-300 text-sm">Paid Invoices</p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$numunpaidinvoices}</h3>
                <p class="text-gray-300 text-sm">Unpaid Invoices</p>
            </div>
        </div>

        <div class="panel">
            <div class="panel-body text-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="euro" class="w-6 h-6 text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">{$totalbalance}</h3>
                <p class="text-gray-300 text-sm">Outstanding Balance</p>
            </div>
        </div>
    </div>

    <!-- Invoice Filters -->
    <div class="mb-6">
        <div class="flex flex-wrap gap-2">
            <button class="btn btn-sm {if !$filterStatus}btn-primary{else}btn-secondary{/if}" onclick="filterInvoices('all')">
                All Invoices
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Paid'}btn-primary{else}btn-secondary{/if}" onclick="filterInvoices('paid')">
                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                Paid
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Unpaid'}btn-primary{else}btn-secondary{/if}" onclick="filterInvoices('unpaid')">
                <i data-lucide="clock" class="w-3 h-3 mr-1"></i>
                Unpaid
            </button>
            <button class="btn btn-sm {if $filterStatus eq 'Overdue'}btn-primary{else}btn-secondary{/if}" onclick="filterInvoices('overdue')">
                <i data-lucide="alert-circle" class="w-3 h-3 mr-1"></i>
                Overdue
            </button>
        </div>
    </div>

    {if $invoices}
        <!-- Invoices Table -->
        <div class="panel">
            <div class="panel-body">
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                {if $masspay}
                                <th width="30">
                                    <input type="checkbox" id="select-all" onchange="toggleAllInvoices(this)">
                                </th>
                                {/if}
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {foreach from=$invoices item=invoice}
                            <tr class="invoice-row" data-status="{$invoice.status|lower}">
                                {if $masspay}
                                <td>
                                    {if $invoice.status eq 'Unpaid'}
                                    <input type="checkbox" name="invoiceids[]" value="{$invoice.id}" class="invoice-checkbox">
                                    {/if}
                                </td>
                                {/if}
                                <td>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center mr-3">
                                            <i data-lucide="file-text" class="w-4 h-4 text-blue-400"></i>
                                        </div>
                                        <span class="font-semibold text-white">#{$invoice.invoicenum}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-gray-300">{$invoice.datecreated}</span>
                                </td>
                                <td>
                                    <span class="text-gray-300">{$invoice.datedue}</span>
                                </td>
                                <td>
                                    <span class="font-semibold text-white">{$invoice.total}</span>
                                </td>
                                <td>
                                    <span class="badge badge-{$invoice.status|strip_tags|lower}">
                                        {$invoice.status|strip_tags}
                                    </span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <a href="viewinvoice.php?id={$invoice.id}" class="btn btn-sm btn-secondary">
                                            <i data-lucide="eye" class="w-3 h-3 mr-1"></i>
                                            View
                                        </a>
                                        {if $invoice.status eq 'Unpaid'}
                                        <a href="viewinvoice.php?id={$invoice.id}" class="btn btn-sm btn-success">
                                            <i data-lucide="credit-card" class="w-3 h-3 mr-1"></i>
                                            Pay
                                        </a>
                                        {/if}
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {if $pagination}
        <div class="mt-8">
            {$pagination}
        </div>
        {/if}

    {else}
        <!-- Empty State -->
        <div class="panel">
            <div class="panel-body text-center py-16">
                <div class="w-24 h-24 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i data-lucide="file-text" class="w-12 h-12 text-gray-400"></i>
                </div>
                <h3 class="text-2xl font-semibold text-white mb-4">No Invoices Found</h3>
                <p class="text-gray-400 mb-8 max-w-md mx-auto">
                    You don't have any invoices yet. Your invoices will appear here once you place an order.
                </p>
                <a href="{$WEB_ROOT}/cart.php" class="btn btn-primary">
                    <i data-lucide="shopping-cart" class="w-4 h-4 mr-2"></i>
                    Browse Services
                </a>
            </div>
        </div>
    {/if}

    <!-- Payment Methods -->
    <div class="mt-8">
        <div class="panel">
            <div class="panel-heading">
                <h3 class="flex items-center">
                    <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-emerald-400"></i>
                    Payment Methods
                </h3>
            </div>
            <div class="panel-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="flex items-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/50">
                        <div class="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="credit-card" class="w-5 h-5 text-blue-400"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Credit Card</h4>
                            <p class="text-gray-400 text-sm">Visa, MasterCard, Amex</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/50">
                        <div class="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="smartphone" class="w-5 h-5 text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">PayPal</h4>
                            <p class="text-gray-400 text-sm">Secure online payments</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/50">
                        <div class="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="coins" class="w-5 h-5 text-orange-400"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Cryptocurrency</h4>
                            <p class="text-gray-400 text-sm">Bitcoin, Ethereum</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-slate-800/30 rounded-lg border border-slate-700/50">
                        <div class="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mr-3">
                            <i data-lucide="banknote" class="w-5 h-5 text-green-400"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Bank Transfer</h4>
                            <p class="text-gray-400 text-sm">SEPA, Wire transfer</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Billing Tips -->
    <div class="mt-8">
        <div class="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6">
            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                <i data-lucide="lightbulb" class="w-5 h-5 mr-2 text-yellow-400"></i>
                Billing Tips
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="calendar" class="w-4 h-4 text-blue-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Auto-Pay Setup</h4>
                        <p class="text-gray-400 text-sm">Enable automatic payments to avoid service interruptions.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="bell" class="w-4 h-4 text-emerald-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Payment Reminders</h4>
                        <p class="text-gray-400 text-sm">We'll send email reminders before invoices are due.</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                        <i data-lucide="percent" class="w-4 h-4 text-orange-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-white mb-1">Annual Discounts</h4>
                        <p class="text-gray-400 text-sm">Save money by choosing annual billing cycles.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function filterInvoices(status) {
    const rows = document.querySelectorAll('.invoice-row');
    const buttons = document.querySelectorAll('[onclick^="filterInvoices"]');
    
    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-secondary');
    });
    event.target.classList.remove('btn-secondary');
    event.target.classList.add('btn-primary');
    
    // Filter rows
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = 'table-row';
        } else {
            row.style.display = 'none';
        }
    });
}

function toggleAllInvoices(checkbox) {
    const invoiceCheckboxes = document.querySelectorAll('.invoice-checkbox');
    invoiceCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateMassPayButton();
}

function updateMassPayButton() {
    const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
    const massPayBtn = document.getElementById('mass-pay-btn');
    if (massPayBtn) {
        massPayBtn.disabled = checkedBoxes.length === 0;
    }
}

function massPayInvoices() {
    const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one invoice to pay.');
        return;
    }
    
    const invoiceIds = Array.from(checkedBoxes).map(cb => cb.value);
    window.location.href = 'viewinvoice.php?id=' + invoiceIds.join(',');
}

// Add event listeners to checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.invoice-checkbox');
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateMassPayButton);
    });
    updateMassPayButton();
});
</script>

{include file="$template/footer.tpl"}
