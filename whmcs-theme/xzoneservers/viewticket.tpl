{include file="$template/header.tpl"}

{if $invalidTicketId}
    <div class="max-w-4xl mx-auto py-8 px-4">
        <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-6 text-center">
            <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-4 text-red-400"></i>
            <h2 class="text-xl font-bold text-white mb-2">Ticket Not Found</h2>
            <p class="text-red-300">{$LANG.supportticketinvalid}</p>
            <a href="supporttickets.php" class="inline-block mt-4 px-6 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-700">
                ← Back to Tickets
            </a>
        </div>
    </div>
{else}
    <div class="max-w-6xl mx-auto py-8 px-4">
        <!-- Ticket Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="ticket" class="w-3 h-3 mr-1.5"></i>
                    Ticket #{$tid}
                </div>
                {if $status eq "Open"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="circle" class="w-3 h-3 mr-1.5 fill-current"></i>
                        {$status}
                    </div>
                {elseif $status eq "Answered"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="message-circle" class="w-3 h-3 mr-1.5"></i>
                        {$status}
                    </div>
                {elseif $status eq "Customer-Reply"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-orange-500/20 to-amber-500/20 border border-orange-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="clock" class="w-3 h-3 mr-1.5"></i>
                        Awaiting Response
                    </div>
                {elseif $status eq "Closed"}
                    <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-slate-500/20 to-gray-500/20 border border-slate-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                        <i data-lucide="x-circle" class="w-3 h-3 mr-1.5"></i>
                        {$status}
                    </div>
                {/if}
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    {$subject}
                </span>
            </h1>
            
            <div class="flex items-center gap-4 text-sm text-gray-400">
                <div class="flex items-center">
                    <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                    {$date}
                </div>
                <div class="flex items-center">
                    <i data-lucide="folder" class="w-4 h-4 mr-1"></i>
                    {$department}
                </div>
                <div class="flex items-center">
                    <i data-lucide="flag" class="w-4 h-4 mr-1"></i>
                    {$urgency} Priority
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        {if $closedticket}
            <div class="bg-gradient-to-r from-orange-500/10 to-amber-500/10 border border-orange-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="info" class="w-5 h-5 mr-3 text-orange-400"></i>
                    <span class="text-orange-300 font-medium">{$LANG.supportticketclosedmsg}</span>
                </div>
            </div>
        {/if}

        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <div class="text-red-300 font-medium">{$errormessage}</div>
                </div>
            </div>
        {/if}

        <!-- Reply Form (Collapsible) -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl mb-8{if !$postingReply} opacity-75{/if}">
            <div class="flex items-center justify-between mb-4 cursor-pointer" onclick="toggleReplyForm()">
                <h3 class="text-xl font-semibold text-white flex items-center">
                    <i data-lucide="pencil" class="w-5 h-5 mr-2 text-purple-400"></i>
                    {$LANG.supportticketsreply}
                </h3>
                <i data-lucide="{if !$postingReply}plus{else}minus{/if}" class="w-5 h-5 text-gray-400" id="replyToggleIcon"></i>
            </div>
            
            <div id="replyFormContainer" class="{if !$postingReply}hidden{/if}">
                <form method="post" action="{$smarty.server.PHP_SELF}?tid={$tid}&c={$c}&postreply=true" enctype="multipart/form-data" class="space-y-4">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="inputName" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsclientname}</label>
                            <input type="text" name="replyname" id="inputName" value="{$replyname}" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white{if $loggedin} opacity-50{/if}"{if $loggedin} disabled{/if}>
                        </div>
                        <div>
                            <label for="inputEmail" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsclientemail}</label>
                            <input type="text" name="replyemail" id="inputEmail" value="{$replyemail}" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white{if $loggedin} opacity-50{/if}"{if $loggedin} disabled{/if}>
                        </div>
                    </div>

                    <div>
                        <label for="inputMessage" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.contactmessage}</label>
                        <textarea name="replymessage" id="inputMessage" rows="8" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 resize-y markdown-editor" data-auto-save-name="ctr{$tid}" placeholder="Enter your reply...">{$replymessage}</textarea>
                    </div>

                    <div>
                        <label for="inputAttachments" class="block text-sm font-medium text-gray-300 mb-2">{$LANG.supportticketsticketattachments}</label>
                        <input type="file" name="attachments[]" id="inputAttachments" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300">
                        <div id="fileUploadsContainer"></div>
                        <button type="button" onclick="extraTicketAttachment()" class="mt-2 px-4 py-2 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg text-sm">
                            Add More Files
                        </button>
                    </div>

                    <div class="flex justify-center pt-4">
                        <button type="submit" class="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300">
                            <i data-lucide="send" class="w-4 h-4 mr-2 inline"></i>
                            {$LANG.supportticketsreply}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Ticket Thread -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                <i data-lucide="message-square" class="w-5 h-5 mr-2 text-blue-400"></i>
                Ticket History
            </h3>
            
            <!-- Debug Info -->
            <div class="bg-slate-800 p-3 rounded mb-4 text-sm">
                <p class="text-gray-300">Descreplies exists: {if $descreplies}YES ({$descreplies|count} messages){else}NO{/if}</p>
                <p class="text-gray-300">Subject: {$subject}</p>
                <p class="text-gray-300">Status: {$status}</p>
            </div>
            
            {if $descreplies}
                {foreach from=$descreplies item=reply}
                    <div class="border-l-4 {if $reply.admin}border-blue-500{else}border-purple-500{/if} pl-4 mb-6 last:mb-0">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-gradient-to-br {if $reply.admin}from-blue-500/20 to-cyan-500/20 border border-blue-500/30{else}from-purple-500/20 to-pink-500/20 border border-purple-500/30{/if} rounded-full flex items-center justify-center">
                                    <i data-lucide="{if $reply.admin}shield-check{else}user{/if}" class="w-4 h-4 {if $reply.admin}text-blue-400{else}text-purple-400{/if}"></i>
                                </div>
                                <div>
                                    <span class="font-semibold text-white">{$reply.requestor.name}</span>
                                    {if $reply.admin}
                                        <span class="ml-2 px-2 py-1 bg-blue-500/20 border border-blue-500/30 rounded text-xs text-blue-300">
                                            {$LANG.supportticketsstaff}
                                        </span>
                                    {else}
                                        <span class="ml-2 px-2 py-1 bg-purple-500/20 border border-purple-500/30 rounded text-xs text-purple-300">
                                            Customer
                                        </span>
                                    {/if}
                                </div>
                            </div>
                            <span class="text-sm text-gray-400">{$reply.date}</span>
                        </div>
                        
                        <div class="bg-slate-800/30 rounded-lg p-4 mb-3">
                            <div class="text-gray-300 prose prose-invert max-w-none">
                                {$reply.message}
                            </div>
                            {if $reply.ipaddress}
                                <div class="mt-3 pt-3 border-t border-slate-600/50">
                                    <span class="text-xs text-gray-500">
                                        <i data-lucide="globe" class="w-3 h-3 mr-1 inline"></i>
                                        IP: {$reply.ipaddress}
                                    </span>
                                </div>
                            {/if}
                        </div>
                        
                        {if $reply.attachments}
                            <div class="flex flex-wrap gap-2">
                                <span class="text-sm text-gray-400 mb-2 w-full">
                                    <i data-lucide="paperclip" class="w-3 h-3 mr-1 inline"></i>
                                    Attachments ({$reply.attachments|count}):
                                </span>
                                {foreach from=$reply.attachments key=num item=attachment}
                                    {if $reply.attachments_removed}
                                        <span class="flex items-center px-3 py-1 bg-red-500/10 border border-red-500/30 rounded text-sm text-red-400">
                                            <i data-lucide="file-x" class="w-3 h-3 mr-1"></i>
                                            {$attachment} (Removed)
                                        </span>
                                    {else}
                                        <a href="dl.php?type={if $reply.id}ar&id={$reply.id}{else}a&id={$id}{/if}&i={$num}" 
                                           class="flex items-center px-3 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-sm text-blue-400 hover:text-blue-300 transition-colors">
                                            <i data-lucide="download" class="w-3 h-3 mr-1"></i>
                                            {$attachment}
                                        </a>
                                    {/if}
                                {/foreach}
                            </div>
                        {/if}
                        
                        {if $reply.id && $reply.admin && $ratingenabled}
                            <div class="mt-3 pt-3 border-t border-slate-600/50">
                                {if $reply.rating}
                                    <div class="flex items-center text-sm text-gray-400">
                                        <span class="mr-2">Rating:</span>
                                        {for $rating=1 to 5}
                                            <i data-lucide="star" class="w-4 h-4 {if (5 - $reply.rating) < $rating}text-yellow-400 fill-current{else}text-gray-600{/if}"></i>
                                        {/for}
                                        <span class="ml-2 text-yellow-400">Rated</span>
                                    </div>
                                {else}
                                    <div class="flex items-center text-sm text-gray-400">
                                        <span class="mr-2">Rate this response:</span>
                                        <div class="flex rating" ticketid="{$tid}" ticketkey="{$c}" ticketreplyid="{$reply.id}">
                                            {for $rating=5 to 1 step -1}
                                                <i data-lucide="star" class="w-4 h-4 text-gray-600 hover:text-yellow-400 cursor-pointer star" rate="{$rating}"></i>
                                            {/for}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        {/if}
                    </div>
                {/foreach}
            {else}
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-slate-800/50 border border-slate-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i data-lucide="message-circle" class="w-8 h-8 text-gray-400"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-white mb-2">No Messages Yet</h4>
                    <p class="text-gray-400">This ticket doesn't have any messages yet. Use the reply form above to add a message.</p>
                </div>
            {/if}
        </div>

        <!-- Actions -->
        <div class="flex justify-center mt-8">
            <a href="supporttickets.php" class="px-6 py-2 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-300">
                <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>
                Back to Tickets
            </a>
        </div>
    </div>
{/if}

<script>
function toggleReplyForm() {
    const container = document.getElementById('replyFormContainer');
    const icon = document.getElementById('replyToggleIcon');
    
    if (container.classList.contains('hidden')) {
        container.classList.remove('hidden');
        icon.setAttribute('data-lucide', 'minus');
    } else {
        container.classList.add('hidden');
        icon.setAttribute('data-lucide', 'plus');
    }
    
    // Re-initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}

function extraTicketAttachment() {
    const container = document.getElementById('fileUploadsContainer');
    const input = document.createElement('input');
    input.type = 'file';
    input.name = 'attachments[]';
    input.className = 'w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300 mt-2';
    container.appendChild(input);
}
</script>

{include file="$template/footer.tpl"}