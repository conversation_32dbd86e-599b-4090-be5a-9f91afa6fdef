{include file="$template/header.tpl"}

<!-- Compact Password Reset Validation Section -->
<section class="py-6 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md mx-auto">
        <!-- Compact <PERSON> Badges -->
        <div class="flex items-center justify-center gap-3 mb-4">
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                Verification
            </div>
            <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                <i data-lucide="key" class="w-3 h-3 mr-1.5"></i>
                New Password
            </div>
        </div>

        <!-- Page Title -->
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-white mb-2">
                <i data-lucide="lock-keyhole" class="w-6 h-6 mr-2 inline text-purple-400"></i>
                Set New Password
            </h1>
            <p class="text-gray-400 text-sm">
                Enter your new password below to complete the reset process
            </p>
        </div>

        <!-- Compact Password Reset Validation Card -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <!-- Alert Messages -->
            {if $invalidkey}
                <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                        <div>
                            <span class="text-red-300 font-medium block">{$LANG.pwresetinvalidkey}</span>
                            <span class="text-red-400 text-sm">Please request a new password reset link</span>
                        </div>
                    </div>
                </div>
            {/if}

            {if $errormessage}
                <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                        <span class="text-red-300 font-medium">{$errormessage}</span>
                    </div>
                </div>
            {/if}

            {if $successmessage}
                <div class="bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/30 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-green-400"></i>
                        <div>
                            <span class="text-green-300 font-medium block">{$successmessage}</span>
                            <span class="text-green-400 text-sm">You can now login with your new password</span>
                        </div>
                    </div>
                </div>
            {/if}

            {if !$invalidkey && !$successmessage}
                <form method="post" action="pwreset.php" class="space-y-4 pwreset-validation-form" role="form">
                    <input type="hidden" name="key" value="{$key}">
                    
                    <div class="space-y-1">
                        <label for="inputNewPassword" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                            {$LANG.newpassword}
                        </label>
                        <input type="password"
                               name="newpw"
                               id="inputNewPassword"
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                               placeholder="Enter your new password"
                               autofocus
                               required>
                        <div class="text-xs text-gray-400 mt-1">
                            Minimum 8 characters with uppercase, lowercase, and numbers
                        </div>
                    </div>

                    <div class="space-y-1">
                        <label for="inputConfirmPassword" class="block text-sm font-medium text-gray-300">
                            <i data-lucide="lock-keyhole" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                            {$LANG.confirmnewpassword}
                        </label>
                        <input type="password"
                               name="confirmpw"
                               id="inputConfirmPassword"
                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                               placeholder="Confirm your new password"
                               required>
                    </div>

                    {if $captcha}
                        <div class="space-y-1">
                            <label class="block text-sm font-medium text-gray-300">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                Security Verification
                            </label>
                            <div class="captcha-container bg-slate-800/30 p-3 rounded-lg border border-slate-600/50">
                                {$captcha}
                            </div>
                        </div>
                    {/if}

                    <button type="submit" 
                            class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all duration-300 shadow-lg hover:shadow-purple-500/25">
                        <i data-lucide="check" class="w-4 h-4 mr-2 inline"></i>
                        {$LANG.pwresetvalidatesubmit}
                    </button>
                </form>
            {/if}

            {if $successmessage}
                <!-- Login Button -->
                <div class="mt-6">
                    <a href="login.php" 
                       class="w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 shadow-lg hover:shadow-blue-500/25">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        Login Now
                    </a>
                </div>
            {else}
                <!-- Back to Login Link -->
                <div class="mt-6 text-center">
                    <a href="login.php" class="inline-flex items-center text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium">
                        <i data-lucide="arrow-left" class="w-4 h-4 mr-1"></i>
                        Back to Login
                    </a>
                </div>
            {/if}
        </div>

        <!-- Compact Footer -->
        <div class="mt-4 text-center">
            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500">
                <a href="contact.php" class="hover:text-gray-300 transition-colors">Support</a>
                <span>•</span>
                <a href="privacy.php" class="hover:text-gray-300 transition-colors">Privacy</a>
                <span>•</span>
                <a href="terms.php" class="hover:text-gray-300 transition-colors">Terms</a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Form validation
    const form = document.querySelector('.pwreset-validation-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('inputNewPassword').value;
            const confirmPassword = document.getElementById('inputConfirmPassword').value;
            
            // Password strength validation
            if (newPassword.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long.');
                return false;
            }
            
            // Check for uppercase, lowercase, and numbers
            const hasUpper = /[A-Z]/.test(newPassword);
            const hasLower = /[a-z]/.test(newPassword);
            const hasNumber = /\d/.test(newPassword);
            
            if (!hasUpper || !hasLower || !hasNumber) {
                e.preventDefault();
                alert('Password must contain at least one uppercase letter, one lowercase letter, and one number.');
                return false;
            }
            
            // Password confirmation validation
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match. Please try again.');
                return false;
            }
        });
        
        // Real-time password confirmation validation
        const confirmField = document.getElementById('inputConfirmPassword');
        const newPasswordField = document.getElementById('inputNewPassword');
        
        if (confirmField && newPasswordField) {
            confirmField.addEventListener('input', function() {
                if (this.value && this.value !== newPasswordField.value) {
                    this.style.borderColor = '#ef4444';
                } else {
                    this.style.borderColor = '';
                }
            });
        }
    }
});
</script>

{include file="$template/footer.tpl"}
