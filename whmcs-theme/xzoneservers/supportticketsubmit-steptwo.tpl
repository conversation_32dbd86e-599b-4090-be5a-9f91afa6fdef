{include file="$template/header.tpl"}

<!-- Ticket Submission Form -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-6xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="edit-3" class="w-3 h-3 mr-1.5"></i>
                    Step 2 of 2
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="clock" class="w-3 h-3 mr-1.5"></i>
                    24/7 Support
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Submit Support Ticket
                </span>
            </h1>
            <p class="text-lg text-gray-300">Provide details about your issue to help our team assist you quickly</p>
        </div>

        <!-- Alert Messages -->
        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <div class="text-red-300 font-medium">{$errormessage}</div>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <form method="post" action="{$smarty.server.PHP_SELF}?step=3" enctype="multipart/form-data" class="space-y-6">
                        
                        <!-- Hidden Fields -->
                        <input type="hidden" name="deptid" value="{$deptid}" />
                        {if $token}
                            <input type="hidden" name="token" value="{$token}" />
                        {/if}
                        
                        <!-- Contact Information (for guests) -->
                        {if !$loggedin}
                            <div class="border-b border-slate-700/50 pb-6">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="user" class="w-5 h-5 mr-2 text-purple-400"></i>
                                    Contact Information
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="space-y-1">
                                        <label for="inputName" class="block text-sm font-medium text-gray-300">
                                            <i data-lucide="user" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                            {$LANG.supportticketsclientname} <span class="text-red-400">*</span>
                                        </label>
                                        <input type="text" 
                                               name="name" 
                                               id="inputName" 
                                               value="{$name}"
                                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                               required>
                                    </div>
                                    <div class="space-y-1">
                                        <label for="inputEmail" class="block text-sm font-medium text-gray-300">
                                            <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                            {$LANG.supportticketsclientemail} <span class="text-red-400">*</span>
                                        </label>
                                        <input type="email" 
                                               name="email" 
                                               id="inputEmail" 
                                               value="{$email}"
                                               class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                               required>
                                    </div>
                                </div>
                            </div>
                        {/if}

                        <!-- Ticket Configuration -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-1">
                                <label for="inputDepartment" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="folder" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                    {$LANG.supportticketsdepartment} <span class="text-red-400">*</span>
                                </label>
                                <select name="deptid" 
                                        id="inputDepartment" 
                                        class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300"
                                        onchange="refreshCustomFields(this)"
                                        required>
                                    {foreach from=$departments item=department}
                                        <option value="{$department.id}"{if $department.id eq $deptid} selected{/if}>
                                            {$department.name}
                                        </option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="space-y-1">
                                <label for="inputPriority" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="flag" class="w-4 h-4 mr-2 inline text-orange-400"></i>
                                    {$LANG.supportticketspriority} <span class="text-red-400">*</span>
                                </label>
                                <select name="urgency" 
                                        id="inputPriority" 
                                        class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300"
                                        required>
                                    <option value="High"{if $urgency eq "High"} selected{/if}>
                                        {$LANG.supportticketsticketurgencyhigh} - Critical issues
                                    </option>
                                    <option value="Medium"{if $urgency eq "Medium" || !$urgency} selected{/if}>
                                        {$LANG.supportticketsticketurgencymedium} - General support
                                    </option>
                                    <option value="Low"{if $urgency eq "Low"} selected{/if}>
                                        {$LANG.supportticketsticketurgencylow} - Basic inquiries
                                    </option>
                                </select>
                            </div>
                        </div>

                        {if $relatedservices}
                            <div class="space-y-1">
                                <label for="inputRelatedService" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="server" class="w-4 h-4 mr-2 inline text-cyan-400"></i>
                                    {$LANG.relatedservice} (Optional)
                                </label>
                                <select name="relatedservice" 
                                        id="inputRelatedService" 
                                        class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50 transition-all duration-300">
                                    <option value="">{$LANG.none}</option>
                                    {foreach from=$relatedservices item=relatedservice}
                                        <option value="{$relatedservice.id}"{if $relatedservice.id eq $selectedservice} selected{/if}>
                                            {$relatedservice.name} ({$relatedservice.status})
                                        </option>
                                    {/foreach}
                                </select>
                            </div>
                        {/if}

                        <div class="space-y-1">
                            <label for="inputSubject" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="type" class="w-4 h-4 mr-2 inline text-pink-400"></i>
                                {$LANG.supportticketsticketsubject} <span class="text-red-400">*</span>
                            </label>
                            <input type="text" 
                                   name="subject" 
                                   id="inputSubject" 
                                   value="{$subject}" 
                                   class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50 transition-all duration-300"
                                   placeholder="Brief description of your issue"
                                   required>
                        </div>

                        <div class="space-y-1">
                            <label for="inputMessage" class="block text-sm font-medium text-gray-300">
                                <i data-lucide="message-square" class="w-4 h-4 mr-2 inline text-violet-400"></i>
                                {$LANG.contactmessage} <span class="text-red-400">*</span>
                            </label>
                            <textarea name="message" 
                                      id="inputMessage" 
                                      rows="12" 
                                      class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 transition-all duration-300 resize-y markdown-editor"
                                      data-auto-save-name="client_ticket_open"
                                      placeholder="Please provide detailed information about your issue, including:&#10;- What you were trying to do&#10;- What actually happened&#10;- Any error messages you received&#10;- Steps to reproduce the problem"
                                      required>{$message}</textarea>
                            <div class="text-sm text-gray-400 mt-2 flex items-center">
                                <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                                The more details you provide, the faster we can help resolve your issue.
                            </div>
                        </div>

                        <!-- File Attachments -->
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-300">
                                <i data-lucide="paperclip" class="w-4 h-4 mr-2 inline text-teal-400"></i>
                                {$LANG.supportticketsticketattachments} (Optional)
                            </label>
                            <div class="space-y-2">
                                <input type="file" 
                                       name="attachments[]" 
                                       id="inputAttachments" 
                                       class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300 hover:file:bg-blue-500/30 transition-all duration-300"
                                       accept="{$allowedfiletypes}">
                                <div id="fileUploadsContainer"></div>
                                <button type="button" 
                                        onclick="extraTicketAttachment()" 
                                        class="px-4 py-2 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg text-sm transition-all duration-300">
                                    <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>
                                    {$LANG.addmore}
                                </button>
                            </div>
                            <div class="text-sm text-gray-400 flex items-center">
                                <i data-lucide="info" class="w-3 h-3 mr-1"></i>
                                {$LANG.supportticketsallowedextensions}: {$allowedfiletypes} ({lang key="maxFileSize" fileSize="$uploadMaxFileSize"})
                            </div>
                        </div>

                        <!-- Custom Fields -->
                        <div id="customFieldsContainer">
                            {include file="$template/supportticketsubmit-customfields.tpl"}
                        </div>

                        <!-- Auto-Answer Suggestions -->
                        <div id="autoAnswerSuggestions" class="hidden bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                            <!-- Auto-suggestions will be loaded here -->
                        </div>

                        <!-- Captcha -->
                        {if $captcha}
                            <div class="border-t border-slate-700/50 pt-6">
                                <label class="block text-sm font-medium text-gray-300 mb-3">
                                    <i data-lucide="shield-check" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                    Security Verification
                                </label>
                                <div class="bg-slate-800/30 p-4 rounded-lg border border-slate-600/50">
                                    {$captcha}
                                </div>
                            </div>
                        {/if}

                        <!-- Submit Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-slate-700/50 text-center">
                            <input type="submit" 
                                   id="openTicketSubmit"
                                   value="{$LANG.supportticketsticketsubmit}"
                                   class="flex-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-slate-900 disable-on-click{if $captcha}{$captcha->getButtonClass($captchaForm)}{/if}" />
                            <a href="supporttickets.php" 
                               class="flex-1 text-center bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-6 py-3 rounded-lg font-medium transition-all duration-300">
                                <i data-lucide="x" class="w-4 h-4 mr-2 inline"></i>
                                {$LANG.cancel}
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Support Information Sidebar -->
            <div class="lg:col-span-1 space-y-6">
            

                <!-- Quick Help -->
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="help-circle" class="w-5 h-5 mr-2 text-yellow-400"></i>
                        Before You Submit
                    </h3>
                    <div class="space-y-4">
                        <a href="knowledgebase.php" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                            <i data-lucide="book" class="w-4 h-4 mr-2"></i>
                            Check Knowledge Base
                        </a>
                        <a href="serverstatus.php" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors">
                            <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                            View System Status
                        </a>
                        <a href="supporttickets.php" class="flex items-center text-purple-400 hover:text-purple-300 transition-colors">
                            <i data-lucide="list" class="w-4 h-4 mr-2"></i>
                            View Your Tickets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Add extra file attachment input
function extraTicketAttachment() {
    const container = document.getElementById('fileUploadsContainer');
    const input = document.createElement('input');
    input.type = 'file';
    input.name = 'attachments[]';
    input.className = 'w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white file:mr-4 file:py-1.5 file:px-3 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-500/20 file:text-blue-300 hover:file:bg-blue-500/30 transition-all duration-300 mt-2';
    input.accept = '{$allowedfiletypes}';
    container.appendChild(input);
}

// Refresh custom fields when department changes
function refreshCustomFields(select) {
    const deptId = select.value;
    if (deptId) {
        // This would typically make an AJAX call to refresh custom fields
        // For now, we'll just reload the custom fields container
        console.log('Department changed to:', deptId);
    }
}
</script>

{include file="$template/footer.tpl"}