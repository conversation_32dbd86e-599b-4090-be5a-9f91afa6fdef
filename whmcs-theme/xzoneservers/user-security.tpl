{include file="$template/header.tpl"}

{* Include the security content without header/footer *}
<!-- Security Settings Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="shield" class="w-3 h-3 mr-1.5"></i>
                    Account Security
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="lock" class="w-3 h-3 mr-1.5"></i>
                    Advanced Protection
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Security Settings
                </span>
            </h1>
            <p class="text-lg text-gray-300">Manage your account security and authentication preferences</p>
        </div>

        <!-- Alert Messages -->
        {if $successmessage}
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-medium">{$successmessage}</span>
                </div>
            </div>
        {/if}

        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <span class="text-red-300 font-medium">{$errormessage}</span>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="user-cog" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Account Menu
                    </h3>
                    <nav class="space-y-2">
                        <a href="index.php/user/profile" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                            Personal Details
                        </a>
                        <a href="index.php/user/password" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-3"></i>
                            Change Password
                        </a>
                        <a href="index.php/user/security" class="flex items-center px-3 py-2 text-white bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg font-medium">
                            <i data-lucide="shield" class="w-4 h-4 mr-3"></i>
                            Security Settings
                        </a>
                        <a href="index.php/user/emails" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-3"></i>
                            Email Preferences
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Two-Factor Authentication -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h2 class="text-xl font-semibold text-white flex items-center">
                                    <i data-lucide="smartphone" class="w-5 h-5 mr-2 text-emerald-400"></i>
                                    Two-Factor Authentication
                                </h2>
                                <p class="text-gray-400 text-sm mt-1">Add an extra layer of security to your account</p>
                            </div>
                            <div class="flex items-center">
                                {if $twofaenabled}
                                    <span class="px-3 py-1 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-emerald-300">
                                        <i data-lucide="check-circle" class="w-3 h-3 mr-1 inline"></i>
                                        Enabled
                                    </span>
                                {else}
                                    <span class="px-3 py-1 bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-full text-xs font-bold text-orange-300">
                                        <i data-lucide="alert-triangle" class="w-3 h-3 mr-1 inline"></i>
                                        Disabled
                                    </span>
                                {/if}
                            </div>
                        </div>

                        {if $twofaenabled}
                            <div class="space-y-4">
                                <div class="bg-emerald-500/10 border border-emerald-500/20 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i data-lucide="shield-check" class="w-5 h-5 mr-3 text-emerald-400"></i>
                                        <div>
                                            <p class="text-emerald-300 font-medium">Two-Factor Authentication is Active</p>
                                            <p class="text-emerald-400/80 text-sm">Your account is protected with 2FA</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row gap-3">
                                    <button onclick="showBackupCodes()" class="flex-1 bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-all duration-300">
                                        <i data-lucide="key" class="w-4 h-4 mr-2 inline"></i>
                                        View Backup Codes
                                    </button>
                                    <button onclick="regenerateBackupCodes()" class="flex-1 bg-blue-500/20 border border-blue-500/30 text-blue-300 hover:text-blue-200 hover:bg-blue-500/30 px-4 py-2 rounded-lg transition-all duration-300">
                                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2 inline"></i>
                                        Regenerate Codes
                                    </button>
                                    <button onclick="disable2FA()" class="flex-1 bg-red-500/20 border border-red-500/30 text-red-300 hover:text-red-200 hover:bg-red-500/30 px-4 py-2 rounded-lg transition-all duration-300">
                                        <i data-lucide="x-circle" class="w-4 h-4 mr-2 inline"></i>
                                        Disable 2FA
                                    </button>
                                </div>
                            </div>
                        {else}
                            <div class="space-y-4">
                                <div class="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i data-lucide="alert-triangle" class="w-5 h-5 mr-3 text-orange-400"></i>
                                        <div>
                                            <p class="text-orange-300 font-medium">Two-Factor Authentication is Disabled</p>
                                            <p class="text-orange-400/80 text-sm">Your account is vulnerable to unauthorized access</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                        <div class="text-center">
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                                                <i data-lucide="smartphone" class="w-6 h-6 text-blue-400"></i>
                                            </div>
                                            <h4 class="font-semibold text-white mb-2">Authenticator App</h4>
                                            <p class="text-sm text-gray-400 mb-4">Use Google Authenticator, Authy, or similar apps</p>
                                            <button onclick="setup2FA('app')" class="w-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25">
                                                Setup App Authentication
                                            </button>
                                        </div>
                                    </div>

                                    <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                        <div class="text-center">
                                            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center mx-auto mb-3">
                                                <i data-lucide="message-square" class="w-6 h-6 text-emerald-400"></i>
                                            </div>
                                            <h4 class="font-semibold text-white mb-2">SMS Authentication</h4>
                                            <p class="text-sm text-gray-400 mb-4">Receive codes via text message</p>
                                            <button onclick="setup2FA('sms')" class="w-full bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:shadow-lg hover:shadow-emerald-500/25">
                                                Setup SMS Authentication
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    </div>

                    <!-- Login Activity -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="activity" class="w-5 h-5 mr-2 text-purple-400"></i>
                            Recent Login Activity
                        </h2>
                        <p class="text-gray-400 text-sm mb-6">Monitor recent access to your account</p>

                        <div class="space-y-3">
                            {if $loginhistory}
                                {foreach from=$loginhistory item=login}
                                    <div class="flex items-center justify-between p-4 bg-slate-800/30 border border-slate-600/30 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center">
                                                {if $login.successful}
                                                    <i data-lucide="check-circle" class="w-5 h-5 text-emerald-400"></i>
                                                {else}
                                                    <i data-lucide="x-circle" class="w-5 h-5 text-red-400"></i>
                                                {/if}
                                            </div>
                                            <div>
                                                <div class="font-medium text-white">
                                                    {if $login.successful}Successful Login{else}Failed Login Attempt{/if}
                                                </div>
                                                <div class="text-sm text-gray-400">
                                                    {$login.ip} • {$login.location} • {$login.device}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            {$login.datetime}
                                        </div>
                                    </div>
                                {/foreach}
                            {else}
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-slate-800/50 border border-slate-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i data-lucide="clock" class="w-8 h-8 text-gray-400"></i>
                                    </div>
                                    <p class="text-gray-400">No recent login activity to display</p>
                                </div>
                            {/if}
                        </div>

                        <div class="mt-4 pt-4 border-t border-slate-700/50">
                            <button onclick="viewFullHistory()" class="w-full bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-2 rounded-lg transition-all duration-300">
                                <i data-lucide="history" class="w-4 h-4 mr-2 inline"></i>
                                View Full Login History
                            </button>
                        </div>
                    </div>

                    <!-- Active Sessions -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="monitor" class="w-5 h-5 mr-2 text-cyan-400"></i>
                            Active Sessions
                        </h2>
                        <p class="text-gray-400 text-sm mb-6">Manage devices currently logged into your account</p>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-4 bg-emerald-500/10 border border-emerald-500/20 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center">
                                        <i data-lucide="monitor" class="w-5 h-5 text-emerald-400"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-white">
                                            Current Session
                                            <span class="ml-2 px-2 py-0.5 bg-emerald-500/20 border border-emerald-500/30 rounded-full text-xs text-emerald-300">Active</span>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            Chrome on Windows • {$currentip} • Just now
                                        </div>
                                    </div>
                                </div>
                                <div class="text-sm text-emerald-400 font-medium">
                                    This device
                                </div>
                            </div>

                            {if $activesessions}
                                {foreach from=$activesessions item=session}
                                    <div class="flex items-center justify-between p-4 bg-slate-800/30 border border-slate-600/30 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center">
                                                {if $session.device_type eq "mobile"}
                                                    <i data-lucide="smartphone" class="w-5 h-5 text-blue-400"></i>
                                                {elseif $session.device_type eq "tablet"}
                                                    <i data-lucide="tablet" class="w-5 h-5 text-blue-400"></i>
                                                {else}
                                                    <i data-lucide="monitor" class="w-5 h-5 text-blue-400"></i>
                                                {/if}
                                            </div>
                                            <div>
                                                <div class="font-medium text-white">{$session.device}</div>
                                                <div class="text-sm text-gray-400">
                                                    {$session.ip} • {$session.location} • {$session.last_active}
                                                </div>
                                            </div>
                                        </div>
                                        <button onclick="terminateSession('{$session.id}')" class="bg-red-500/20 border border-red-500/30 text-red-300 hover:text-red-200 hover:bg-red-500/30 px-3 py-1 rounded-lg text-sm transition-all duration-300">
                                            <i data-lucide="x" class="w-3 h-3 mr-1 inline"></i>
                                            End Session
                                        </button>
                                    </div>
                                {/foreach}
                            {/if}
                        </div>

                        <div class="mt-4 pt-4 border-t border-slate-700/50">
                            <button onclick="terminateAllSessions()" class="w-full bg-red-500/20 border border-red-500/30 text-red-300 hover:text-red-200 hover:bg-red-500/30 px-4 py-2 rounded-lg transition-all duration-300">
                                <i data-lucide="log-out" class="w-4 h-4 mr-2 inline"></i>
                                End All Other Sessions
                            </button>
                        </div>
                    </div>

                    <!-- Security Notifications -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="bell" class="w-5 h-5 mr-2 text-orange-400"></i>
                            Security Notifications
                        </h2>
                        <p class="text-gray-400 text-sm mb-6">Configure when to receive security alerts</p>

                        <form method="post" action="index.php/user/security" class="space-y-4">
                            <div class="space-y-4">
                                <label class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i data-lucide="log-in" class="w-4 h-4 mr-3 text-blue-400"></i>
                                        <div>
                                            <span class="text-white font-medium">Login Notifications</span>
                                            <p class="text-sm text-gray-400">Get notified of new login attempts</p>
                                        </div>
                                    </div>
                                    <input type="checkbox" name="notify_login" {if $notify_login}checked{/if} class="w-5 h-5 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                                </label>

                                <label class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i data-lucide="key" class="w-4 h-4 mr-3 text-purple-400"></i>
                                        <div>
                                            <span class="text-white font-medium">Password Changes</span>
                                            <p class="text-sm text-gray-400">Get notified when password is changed</p>
                                        </div>
                                    </div>
                                    <input type="checkbox" name="notify_password" {if $notify_password}checked{/if} class="w-5 h-5 text-purple-500 bg-slate-800 border-slate-600 rounded focus:ring-purple-500/50 focus:ring-2">
                                </label>

                                <label class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i data-lucide="shield" class="w-4 h-4 mr-3 text-emerald-400"></i>
                                        <div>
                                            <span class="text-white font-medium">Security Settings</span>
                                            <p class="text-sm text-gray-400">Get notified of security setting changes</p>
                                        </div>
                                    </div>
                                    <input type="checkbox" name="notify_security" {if $notify_security}checked{/if} class="w-5 h-5 text-emerald-500 bg-slate-800 border-slate-600 rounded focus:ring-emerald-500/50 focus:ring-2">
                                </label>

                                <label class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-3 text-orange-400"></i>
                                        <div>
                                            <span class="text-white font-medium">Suspicious Activity</span>
                                            <p class="text-sm text-gray-400">Get notified of unusual account activity</p>
                                        </div>
                                    </div>
                                    <input type="checkbox" name="notify_suspicious" {if $notify_suspicious}checked{/if} class="w-5 h-5 text-orange-500 bg-slate-800 border-slate-600 rounded focus:ring-orange-500/50 focus:ring-2">
                                </label>
                            </div>

                            <div class="pt-4">
                                <button type="submit" 
                                        name="save_notifications"
                                        class="w-full md:w-auto bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-orange-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                                    <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                                    Save Notification Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 2FA Setup Functions
    window.setup2FA = function(method) {
        if (method === 'app') {
            alert('Redirecting to authenticator app setup...');
        } else if (method === 'sms') {
            alert('Redirecting to SMS authentication setup...');
        }
    };

    window.disable2FA = function() {
        if (confirm('Are you sure you want to disable Two-Factor Authentication? This will make your account less secure.')) {
            alert('Two-Factor Authentication will be disabled...');
        }
    };

    window.showBackupCodes = function() {
        alert('Showing backup codes...');
    };

    window.regenerateBackupCodes = function() {
        if (confirm('Are you sure you want to regenerate backup codes? Your old codes will no longer work.')) {
            alert('Generating new backup codes...');
        }
    };

    // Session Management Functions
    window.terminateSession = function(sessionId) {
        if (confirm('Are you sure you want to end this session? The device will be logged out immediately.')) {
            alert('Session terminated: ' + sessionId);
        }
    };

    window.terminateAllSessions = function() {
        if (confirm('Are you sure you want to end all other sessions? All other devices will be logged out immediately.')) {
            alert('All other sessions will be terminated...');
        }
    };

    // Login History Functions
    window.viewFullHistory = function() {
        alert('Showing full login history...');
    };
});
</script>

{include file="$template/footer.tpl"}