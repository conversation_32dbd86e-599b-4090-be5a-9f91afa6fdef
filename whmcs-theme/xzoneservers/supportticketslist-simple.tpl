{include file="$template/header.tpl"}

<div class="max-w-6xl mx-auto py-8 px-4">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                    <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                        Support Tickets
                    </span>
                </h1>
                <p class="text-lg text-gray-300">Manage your support requests and get help from our experts</p>
            </div>
            <div>
                <a href="submitticket.php" class="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300">
                    <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>
                    Open New Ticket
                </a>
            </div>
        </div>
    </div>



    {if $tickets}
        <!-- Tickets List -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <h2 class="text-xl font-semibold text-white mb-6 flex items-center">
                <i data-lucide="list" class="w-5 h-5 mr-2 text-blue-400"></i>
                Your Support Tickets ({$tickets|count})
            </h2>
            
            <div class="space-y-4">
                {foreach from=$tickets item=ticket}
                    <div class="bg-slate-800/50 border border-slate-600/50 rounded-lg p-4 hover:bg-slate-700/50 transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <span class="font-semibold text-white">#{$ticket.tid}</span>
                                    <span class="px-2 py-1 bg-{if $ticket.status eq 'Open'}emerald{elseif $ticket.status eq 'Answered'}blue{elseif $ticket.status eq 'Closed'}slate{else}orange{/if}-500/20 border border-{if $ticket.status eq 'Open'}emerald{elseif $ticket.status eq 'Answered'}blue{elseif $ticket.status eq 'Closed'}slate{else}orange{/if}-500/30 rounded text-xs text-{if $ticket.status eq 'Open'}emerald{elseif $ticket.status eq 'Answered'}blue{elseif $ticket.status eq 'Closed'}slate{else}orange{/if}-300">
                                        {$ticket.status}
                                    </span>
                                    {if $ticket.urgency}
                                        <span class="px-2 py-1 bg-purple-500/20 border border-purple-500/30 rounded text-xs text-purple-300">
                                            {$ticket.urgency}
                                        </span>
                                    {/if}
                                </div>
                                <h3 class="text-white font-medium mb-1">{$ticket.subject}</h3>
                                <div class="flex items-center gap-4 text-sm text-gray-400">
                                    <span>Department: {$ticket.department}</span>
                                    <span>Last Update: {$ticket.lastreply}</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <a href="viewticket.php?tid={$ticket.tid}&c={$ticket.c}" 
                                   class="bg-blue-500/20 border border-blue-500/30 text-blue-300 hover:text-blue-200 hover:bg-blue-500/30 px-4 py-2 rounded-lg transition-all duration-300">
                                    View Ticket
                                </a>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>
        </div>
    {else}
        <!-- No Tickets -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 shadow-2xl text-center">
            <div class="w-16 h-16 bg-slate-800/50 border border-slate-600/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="inbox" class="w-8 h-8 text-gray-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">No Support Tickets</h3>
            <p class="text-gray-400 mb-6">You haven't submitted any support tickets yet. Our expert team is here to help whenever you need assistance.</p>
            <a href="submitticket.php" class="bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300">
                <i data-lucide="plus" class="w-4 h-4 mr-2 inline"></i>
                Create Your First Ticket
            </a>
        </div>
    {/if}

    <!-- Support Information -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Response Times -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                <i data-lucide="clock" class="w-5 h-5 mr-2 text-purple-400"></i>
                Response Times
            </h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-gray-300">Critical Issues</span>
                    <span class="text-red-400 font-semibold">< 15 min</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-300">General Support</span>
                    <span class="text-blue-400 font-semibold">< 2 hours</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
            <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                <i data-lucide="zap" class="w-5 h-5 mr-2 text-yellow-400"></i>
                Quick Actions
            </h3>
            <div class="space-y-3">
                <a href="knowledgebase.php" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                    <i data-lucide="book" class="w-4 h-4 mr-2"></i>
                    Browse Knowledge Base
                </a>
                <a href="serverstatus.php" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors">
                    <i data-lucide="activity" class="w-4 h-4 mr-2"></i>
                    Check System Status
                </a>
            </div>
        </div>
    </div>
</div>

{include file="$template/footer.tpl"}