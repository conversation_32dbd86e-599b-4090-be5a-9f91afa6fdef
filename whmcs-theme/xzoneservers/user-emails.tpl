{include file="$template/header.tpl"}

{* Include the email preferences content without header/footer *}
<!-- Email Preferences Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="mail" class="w-3 h-3 mr-1.5"></i>
                    Email Settings
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="bell" class="w-3 h-3 mr-1.5"></i>
                    Notification Control
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Email Preferences
                </span>
            </h1>
            <p class="text-lg text-gray-300">Customize your email notifications and communication preferences</p>
        </div>

        <!-- Alert Messages -->
        {if $successmessage}
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-medium">{$successmessage}</span>
                </div>
            </div>
        {/if}

        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <span class="text-red-300 font-medium">{$errormessage}</span>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="user-cog" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Account Menu
                    </h3>
                    <nav class="space-y-2">
                        <a href="index.php/user/profile" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                            Personal Details
                        </a>
                        <a href="index.php/user/password" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-3"></i>
                            Change Password
                        </a>
                        <a href="index.php/user/security" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-teal-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="shield" class="w-4 h-4 mr-3"></i>
                            Security Settings
                        </a>
                        <a href="index.php/user/emails" class="flex items-center px-3 py-2 text-white bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-lg font-medium">
                            <i data-lucide="mail" class="w-4 h-4 mr-3"></i>
                            Email Preferences
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    <!-- Email Notification Categories -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="settings" class="w-5 h-5 mr-2 text-blue-400"></i>
                            Notification Settings
                        </h2>
                        <p class="text-gray-400 text-sm mb-6">Choose which emails you'd like to receive from us</p>

                        <form method="post" action="index.php/user/emails" class="space-y-6">
                            <!-- Account & Billing -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-emerald-400"></i>
                                    Account & Billing
                                </h3>
                                <div class="space-y-4">
                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="file-text" class="w-4 h-4 mr-3 text-blue-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Invoice Notifications</span>
                                                <p class="text-sm text-gray-400">New invoices and payment reminders</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_invoices" {if $email_invoices}checked{/if} class="w-5 h-5 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="check-circle" class="w-4 h-4 mr-3 text-emerald-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Payment Confirmations</span>
                                                <p class="text-sm text-gray-400">Successful payment receipts</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_payments" {if $email_payments}checked{/if} class="w-5 h-5 text-emerald-500 bg-slate-800 border-slate-600 rounded focus:ring-emerald-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="alert-triangle" class="w-4 h-4 mr-3 text-orange-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Account Overdue Notices</span>
                                                <p class="text-sm text-gray-400">Overdue payment notifications</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_overdue" {if $email_overdue}checked{/if} class="w-5 h-5 text-orange-500 bg-slate-800 border-slate-600 rounded focus:ring-orange-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="calendar" class="w-4 h-4 mr-3 text-purple-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Service Renewals</span>
                                                <p class="text-sm text-gray-400">Upcoming service renewal reminders</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_renewals" {if $email_renewals}checked{/if} class="w-5 h-5 text-purple-500 bg-slate-800 border-slate-600 rounded focus:ring-purple-500/50 focus:ring-2">
                                    </label>
                                </div>
                            </div>

                            <!-- Service & Support -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="headphones" class="w-5 h-5 mr-2 text-purple-400"></i>
                                    Service & Support
                                </h3>
                                <div class="space-y-4">
                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="ticket" class="w-4 h-4 mr-3 text-blue-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Support Ticket Updates</span>
                                                <p class="text-sm text-gray-400">New replies and status changes</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_tickets" {if $email_tickets}checked{/if} class="w-5 h-5 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="server" class="w-4 h-4 mr-3 text-emerald-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Service Status Updates</span>
                                                <p class="text-sm text-gray-400">Service suspensions, activations, and changes</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_service_status" {if $email_service_status}checked{/if} class="w-5 h-5 text-emerald-500 bg-slate-800 border-slate-600 rounded focus:ring-emerald-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="bell" class="w-4 h-4 mr-3 text-orange-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Maintenance Notifications</span>
                                                <p class="text-sm text-gray-400">Scheduled maintenance and downtime alerts</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_maintenance" {if $email_maintenance}checked{/if} class="w-5 h-5 text-orange-500 bg-slate-800 border-slate-600 rounded focus:ring-orange-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="activity" class="w-4 h-4 mr-3 text-red-400"></i>
                                            <div>
                                                <span class="text-white font-medium">System Alerts</span>
                                                <p class="text-sm text-gray-400">Critical system issues and outages</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_alerts" {if $email_alerts}checked{/if} class="w-5 h-5 text-red-500 bg-slate-800 border-slate-600 rounded focus:ring-red-500/50 focus:ring-2">
                                    </label>
                                </div>
                            </div>

                            <!-- Marketing & Updates -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="megaphone" class="w-5 h-5 mr-2 text-cyan-400"></i>
                                    Marketing & Updates
                                </h3>
                                <div class="space-y-4">
                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="gift" class="w-4 h-4 mr-3 text-purple-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Promotional Offers</span>
                                                <p class="text-sm text-gray-400">Special deals and discount announcements</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_promotions" {if $email_promotions}checked{/if} class="w-5 h-5 text-purple-500 bg-slate-800 border-slate-600 rounded focus:ring-purple-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="newspaper" class="w-4 h-4 mr-3 text-blue-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Newsletter</span>
                                                <p class="text-sm text-gray-400">Monthly updates and company news</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_newsletter" {if $email_newsletter}checked{/if} class="w-5 h-5 text-blue-500 bg-slate-800 border-slate-600 rounded focus:ring-blue-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="zap" class="w-4 h-4 mr-3 text-yellow-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Product Updates</span>
                                                <p class="text-sm text-gray-400">New features and service announcements</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_updates" {if $email_updates}checked{/if} class="w-5 h-5 text-yellow-500 bg-slate-800 border-slate-600 rounded focus:ring-yellow-500/50 focus:ring-2">
                                    </label>

                                    <label class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i data-lucide="users" class="w-4 h-4 mr-3 text-emerald-400"></i>
                                            <div>
                                                <span class="text-white font-medium">Community Events</span>
                                                <p class="text-sm text-gray-400">Webinars, conferences, and community activities</p>
                                            </div>
                                        </div>
                                        <input type="checkbox" name="email_events" {if $email_events}checked{/if} class="w-5 h-5 text-emerald-500 bg-slate-800 border-slate-600 rounded focus:ring-emerald-500/50 focus:ring-2">
                                    </label>
                                </div>
                            </div>

                            <!-- Email Format Preferences -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="type" class="w-5 h-5 mr-2 text-pink-400"></i>
                                    Email Format
                                </h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm text-gray-400 mb-3">Choose how you'd like to receive emails</p>
                                        <div class="space-y-3">
                                            <label class="flex items-center">
                                                <input type="radio" name="email_format" value="html" {if $email_format eq "html" or not $email_format}checked{/if} class="w-4 h-4 text-pink-500 bg-slate-800 border-slate-600 focus:ring-pink-500/50 focus:ring-2">
                                                <div class="ml-3">
                                                    <span class="text-white font-medium">HTML (Recommended)</span>
                                                    <p class="text-sm text-gray-400">Rich formatting with images and styling</p>
                                                </div>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="email_format" value="text" {if $email_format eq "text"}checked{/if} class="w-4 h-4 text-pink-500 bg-slate-800 border-slate-600 focus:ring-pink-500/50 focus:ring-2">
                                                <div class="ml-3">
                                                    <span class="text-white font-medium">Plain Text</span>
                                                    <p class="text-sm text-gray-400">Simple text-only emails</p>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Email Frequency -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                                    <i data-lucide="clock" class="w-5 h-5 mr-2 text-teal-400"></i>
                                    Email Frequency
                                </h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm text-gray-400 mb-3">How often would you like to receive non-critical emails?</p>
                                        <select name="email_frequency" class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-teal-500/50 transition-all duration-300">
                                            <option value="immediate" {if $email_frequency eq "immediate"}selected{/if}>Immediately</option>
                                            <option value="daily" {if $email_frequency eq "daily"}selected{/if}>Daily Digest</option>
                                            <option value="weekly" {if $email_frequency eq "weekly"}selected{/if}>Weekly Summary</option>
                                            <option value="monthly" {if $email_frequency eq "monthly"}selected{/if}>Monthly Roundup</option>
                                        </select>
                                        <p class="text-xs text-gray-400 mt-2">Critical emails (billing, security) are always sent immediately</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="pt-4">
                                <button type="submit" 
                                        name="save_preferences"
                                        class="w-full md:w-auto bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-orange-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                                    <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                                    Save Email Preferences
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="zap" class="w-5 h-5 mr-2 text-yellow-400"></i>
                            Quick Actions
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button onclick="enableAll()" class="bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-3 rounded-lg transition-all duration-300 text-center">
                                <i data-lucide="check-circle" class="w-5 h-5 mx-auto mb-2 text-emerald-400"></i>
                                <div class="font-medium">Enable All</div>
                                <div class="text-xs text-gray-400">Turn on all notifications</div>
                            </button>
                            
                            <button onclick="disableMarketing()" class="bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-3 rounded-lg transition-all duration-300 text-center">
                                <i data-lucide="x-circle" class="w-5 h-5 mx-auto mb-2 text-orange-400"></i>
                                <div class="font-medium">Disable Marketing</div>
                                <div class="text-xs text-gray-400">Keep only essential emails</div>
                            </button>
                            
                            <button onclick="resetDefaults()" class="bg-slate-800/50 border border-slate-600/50 text-gray-300 hover:text-white hover:bg-slate-700/50 px-4 py-3 rounded-lg transition-all duration-300 text-center">
                                <i data-lucide="rotate-ccw" class="w-5 h-5 mx-auto mb-2 text-blue-400"></i>
                                <div class="font-medium">Reset Defaults</div>
                                <div class="text-xs text-gray-400">Restore recommended settings</div>
                            </button>
                        </div>
                    </div>

                    <!-- Email Statistics -->
                    <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-purple-400"></i>
                            Email Statistics
                        </h2>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-400 mb-1">{if $email_stats.sent}{$email_stats.sent}{else}0{/if}</div>
                                <div class="text-sm text-gray-400">Emails Sent</div>
                                <div class="text-xs text-gray-500">This month</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-emerald-400 mb-1">{if $email_stats.opened}{$email_stats.opened}{else}0{/if}</div>
                                <div class="text-sm text-gray-400">Emails Opened</div>
                                <div class="text-xs text-gray-500">This month</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-400 mb-1">{if $email_stats.clicked}{$email_stats.clicked}{else}0{/if}</div>
                                <div class="text-sm text-gray-400">Links Clicked</div>
                                <div class="text-xs text-gray-500">This month</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-400 mb-1">{if $email_stats.unsubscribed}{$email_stats.unsubscribed}{else}0{/if}</div>
                                <div class="text-sm text-gray-400">Unsubscribed</div>
                                <div class="text-xs text-gray-500">This month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quick action functions
    window.enableAll = function() {
        if (confirm('Enable all email notifications?')) {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }
    };

    window.disableMarketing = function() {
        if (confirm('Disable all marketing emails? You will still receive important account and service notifications.')) {
            // Disable marketing related checkboxes
            const marketingEmails = [
                'email_promotions',
                'email_newsletter', 
                'email_updates',
                'email_events'
            ];
            
            marketingEmails.forEach(name => {
                const checkbox = document.querySelector(`input[name="${name}"]`);
                if (checkbox) {
                    checkbox.checked = false;
                }
            });
        }
    };

    window.resetDefaults = function() {
        if (confirm('Reset all email preferences to recommended defaults?')) {
            // Set recommended default values
            const defaults = {
                'email_invoices': true,
                'email_payments': true,
                'email_overdue': true,
                'email_renewals': true,
                'email_tickets': true,
                'email_service_status': true,
                'email_maintenance': true,
                'email_alerts': true,
                'email_promotions': false,
                'email_newsletter': false,
                'email_updates': true,
                'email_events': false
            };

            Object.keys(defaults).forEach(name => {
                const checkbox = document.querySelector(`input[name="${name}"]`);
                if (checkbox) {
                    checkbox.checked = defaults[name];
                }
            });

            // Set format to HTML and frequency to immediate
            const formatHtml = document.querySelector('input[name="email_format"][value="html"]');
            if (formatHtml) formatHtml.checked = true;

            const frequency = document.querySelector('select[name="email_frequency"]');
            if (frequency) frequency.value = 'immediate';
        }
    };

    // Form submission with validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Check if at least one critical notification is enabled
            const criticalEmails = [
                'email_invoices',
                'email_payments', 
                'email_overdue',
                'email_tickets',
                'email_alerts'
            ];

            const anyCriticalEnabled = criticalEmails.some(name => {
                const checkbox = document.querySelector(`input[name="${name}"]`);
                return checkbox && checkbox.checked;
            });

            if (!anyCriticalEnabled) {
                if (!confirm('Warning: You have disabled all critical notifications. You may miss important account and billing information. Continue anyway?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    }
});
</script>

{include file="$template/footer.tpl"}