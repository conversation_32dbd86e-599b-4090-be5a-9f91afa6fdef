# X-ZoneServers WHMCS Theme

A modern, responsive WHMCS theme designed to match the X-ZoneServers website with dark theme, gradients, and enterprise-grade styling.

## Features

- **Modern Dark Theme**: Sleek dark design with blue/purple gradients
- **Fully Responsive**: Mobile-first design that works on all devices
- **Full-Width Layout**: Clean, spacious layout without sidebar clutter
- **Accessibility Compliant**: WCAG 2.1 AA compliant with keyboard navigation
- **Performance Optimized**: Fast loading with optimized CSS and JavaScript
- **SEO Friendly**: Semantic HTML structure with proper meta tags
- **Icon Integration**: Lucide icons for consistent iconography
- **Animation Support**: Smooth transitions and hover effects
- **Customizable**: Easy color and branding customization

## Installation

1. **Upload Theme Files**
   ```bash
   # Upload the entire 'xzoneservers' folder to your WHMCS templates directory
   /path/to/whmcs/templates/xzoneservers/
   ```

2. **Activate Theme**
   - Login to WHMCS Admin Area
   - Go to Setup > General Settings > General
   - Set "Template" to "xzoneservers"
   - Save Changes

3. **Configure Theme Settings**
   - Go to Setup > General Settings > General
   - Scroll down to "Template Settings"
   - Customize colors, branding, and features as needed

## File Structure

```
xzoneservers/
├── css/
│   ├── styles.css          # Main theme styles
│   ├── responsive.css      # Responsive design rules
│   └── animations.css      # Animation definitions (optional)
├── js/
│   ├── theme.js           # Main theme JavaScript
│   └── animations.js      # Animation scripts (optional)
├── images/
│   ├── favicon.png        # Theme favicon
│   └── logo.png          # Default logo
├── includes/
│   └── customfields.tpl   # Custom fields template
├── header.tpl             # Site header (full-width layout)
├── footer.tpl             # Site footer
├── homepage.tpl           # Homepage template
├── login.tpl              # Login page
├── register.tpl           # Registration page
├── cart.tpl               # Shopping cart
├── clientareahome.tpl     # Client dashboard
├── clientareaservices.tpl # Services list
├── clientareainvoices.tpl # Invoices list
├── viewinvoice.tpl        # Invoice details
├── supporttickets.tpl     # Support tickets list
├── submitticket.tpl       # Submit ticket form
├── template.php           # Theme configuration
├── theme.yaml             # Theme metadata
└── README.md              # This file
```

## Customization

### Colors

Edit the CSS variables in `css/styles.css`:

```css
:root {
    --xz-primary: #0ea5e9;      /* Primary brand color */
    --xz-secondary: #38bdf8;    /* Secondary brand color */
    --xz-accent: #a855f7;       /* Accent color */
    --xz-bg-primary: #020617;   /* Main background */
    --xz-bg-secondary: #0f172a; /* Card backgrounds */
}
```

### Branding

1. **Logo**: Replace `images/logo.png` with your logo
2. **Company Name**: Update in WHMCS General Settings
3. **Colors**: Use the theme configuration in WHMCS admin
4. **Favicon**: Replace `images/favicon.png`

### Theme Configuration

Available configuration options in WHMCS admin:

- **Brand Name**: Your company name
- **Brand Tagline**: Company slogan/tagline
- **Primary Color**: Main brand color
- **Secondary Color**: Secondary brand color
- **Accent Color**: Accent/highlight color
- **Social Media URLs**: Twitter, LinkedIn, GitHub links
- **Support Information**: Email, phone, footer text
- **Feature Toggles**: Animations, gradients, glassmorphism

## Template Files

### Core Templates

- `header.tpl` - Site header with navigation
- `footer.tpl` - Site footer with links and info
- `homepage.tpl` - Homepage with hero section and pricing

### Client Area Templates

- `clientareahome.tpl` - Client dashboard overview
- `clientareaservices.tpl` - Services management
- `clientareainvoices.tpl` - Invoice listing
- `viewinvoice.tpl` - Individual invoice view

### Authentication Templates

- `login.tpl` - Login form with compact design
- `register.tpl` - Registration form with compact design matching login
- `pwreset.tpl` - Password reset request form
- `pwresetvalidation.tpl` - Password reset validation and new password form
- `pwresetconfirmation.tpl` - Password reset confirmation page

### Support Templates

- `supporttickets.tpl` - Support ticket listing
- `submitticket.tpl` - New ticket submission

### E-commerce Templates

- `cart.tpl` - Shopping cart and checkout

## JavaScript Features

The theme includes enhanced JavaScript functionality:

- **Form Validation**: Client-side form validation
- **Loading States**: Visual feedback for form submissions
- **Tooltips**: Custom tooltip system
- **Modal Management**: Accessible modal dialogs
- **Table Enhancements**: Sorting and responsive tables
- **Accessibility**: Keyboard navigation and screen reader support

## CSS Framework

The theme uses a custom CSS framework with:

- **CSS Grid & Flexbox**: Modern layout systems
- **CSS Variables**: Easy customization
- **Mobile-First**: Responsive design approach
- **Component-Based**: Reusable UI components
- **Utility Classes**: Helper classes for common styles

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

- **Optimized CSS**: Minified and compressed
- **Lazy Loading**: Images and non-critical resources
- **Caching**: Browser caching headers
- **CDN Ready**: External resource optimization

## Accessibility

- **WCAG 2.1 AA Compliant**: Meets accessibility standards
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects motion preferences

## SEO Features

- **Semantic HTML**: Proper HTML5 structure
- **Meta Tags**: Comprehensive meta tag support
- **Schema Markup**: Structured data integration
- **Performance**: Fast loading for better rankings

## Support

For theme support and customization:

1. Check the WHMCS documentation
2. Review the theme configuration options
3. Contact X-ZoneServers support team
4. Submit issues via support ticket system

## Updates

To update the theme:

1. Backup your current theme files
2. Upload new theme files
3. Clear WHMCS template cache
4. Test all functionality

## License

This theme is proprietary to X-ZoneServers and is licensed for use with X-ZoneServers WHMCS installations only.

## Changelog

### Version 1.0.0
- Initial release
- Complete WHMCS theme implementation
- Responsive design
- Accessibility features
- Performance optimizations
