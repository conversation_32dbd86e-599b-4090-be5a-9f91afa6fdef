<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="{$charset}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$companyname} - Payment for Invoice #{$invoicenum}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        slate: {
                            850: '#1e293b',
                            900: '#0f172a',
                            950: '#020617'
                        }
                    }
                }
            }
        }
    </script>

    <!-- Lucide Icons -->
    <script defer src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- WHMCS Required Head Output -->
    {$headoutput}

    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
        }

        .panel {
            background: rgba(30, 41, 59, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .panel-heading {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
        }

        .panel-body {
            padding: 1.5rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
        }

        .btn-secondary {
            background: rgba(71, 85, 105, 0.5);
            color: #e2e8f0;
            border: 1px solid rgba(71, 85, 105, 0.5);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
        }

        .form-control {
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 0.5rem;
            color: #e2e8f0;
            padding: 0.75rem;
        }

        .form-control:focus {
            background: rgba(30, 41, 59, 0.7);
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #93c5fd;
        }

        .w-hidden {
            display: none;
        }
    </style>
</head>
<body>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Header -->
        <div class="text-center mb-8">
            <div class="flex items-center justify-center mb-4">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-8 h-8 text-white"></i>
                </div>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                    Secure Payment
                </span>
            </h1>
            <p class="text-gray-300">Complete your payment for Invoice #{$invoicenum}</p>
        </div>

        <div class="max-w-6xl mx-auto">
            <div class="panel">

                <div class="panel-body">
                    {if $showRemoteInput}
                        <div id="frmRemoteCardProcess" class="text-center">
                            {$remoteInput}
                            <iframe name="ccframe" class="auth3d-area w-full h-96 rounded-lg border border-slate-600" scrolling="auto" src="about:blank"></iframe>
                        </div>

                        <script>
                            jQuery("#frmRemoteCardProcess").find("form:first").attr('target', 'ccframe');
                            setTimeout("autoSubmitFormByContainer('frmRemoteCardProcess')", 1000);
                        </script>
                    {else}
                        {include file="twenty-one/payment/$cardOrBank/validate.tpl"}
                        <form id="frmPayment" method="post" action="{$submitLocation}" role="form">
                            <input type="hidden" name="invoiceid" value="{$invoiceid}" />

                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                                <!-- Payment Form -->
                                <div class="lg:col-span-2">

                                    {if $errormessage}
                                        <div class="alert alert-danger">
                                            <i data-lucide="alert-circle" class="w-5 h-5 inline mr-2"></i>
                                            {$errormessage}
                                        </div>
                                    {/if}

                                    <div class="alert alert-danger text-center gateway-errors w-hidden"></div>

                                    <div class="space-y-6">
                                        <div>
                                            <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                                                <i data-lucide="credit-card" class="w-5 h-5 mr-2 text-blue-400"></i>
                                                {lang key="makepayment"}
                                            </h3>
                                        </div>

                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                                    {lang key="paymentmethod"}
                                                </label>
                                                <div class="payment-gateway-selection">
                                                    {include file="twenty-one/payment/$cardOrBank/select.tpl"}
                                                </div>
                                            </div>

                                            {if !$hasRemoteInput}
                                                <div class="payment-inputs">
                                                    {include file="twenty-one/payment/$cardOrBank/inputs.tpl"}
                                                </div>
                                            {/if}

                                            <div class="pt-4">
                                                <button type="submit" class="btn btn-primary w-full text-lg py-4" id="btnSubmit">
                                                    <i data-lucide="lock" class="w-5 h-5 mr-2"></i>
                                                    <span class="pay-text">{lang key="submitpayment"}</span>
                                                    <span class="click-text w-hidden">{lang key="pleasewait"}</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <!-- Invoice Summary -->
                                <div class="lg:col-span-1">
                                    <div class="panel">
                                        <div class="panel-heading">
                                            <h4 class="text-lg font-semibold text-white flex items-center">
                                                <i data-lucide="file-text" class="w-5 h-5 mr-2 text-green-400"></i>
                                                Invoice Summary
                                            </h4>
                                        </div>
                                        <div class="panel-body">
                                            {include file="twenty-one/payment/invoice-summary.tpl"}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {if $servedOverSsl}
                                <div class="alert alert-info mt-6">
                                    <i data-lucide="shield-check" class="w-5 h-5 inline mr-2"></i>
                                    {lang key="creditcardsecuritynotice"}
                                </div>
                            {/if}

                        </form>

                        <script>
                        jQuery(document).ready(function() {
                            jQuery('#inputCardCvv, #inputCardNumber').filter(':visible').first().focus();
                        });
                        </script>
                    {/if}

                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-8">
                <a href="viewinvoice.php?id={$invoiceid}" class="btn btn-secondary">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Invoice
                </a>
            </div>

            <!-- Security Notice -->
            <div class="text-center mt-8 text-gray-400 text-sm">
                <div class="flex items-center justify-center mb-2">
                    <i data-lucide="shield-check" class="w-4 h-4 mr-2 text-green-400"></i>
                    <span>256-bit SSL Encryption</span>
                </div>
                <p>Your payment information is secure and encrypted</p>
            </div>
        </div>
    </div>

    <!-- Initialize Lucide Icons -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>

</body>
</html>
