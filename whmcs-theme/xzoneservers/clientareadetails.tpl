{include file="$template/header.tpl"}

<!-- Account Settings Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="settings" class="w-3 h-3 mr-1.5"></i>
                    Account Management
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                    Secure Profile
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Account Settings
                </span>
            </h1>
            <p class="text-lg text-gray-300">Manage your account information and preferences</p>
        </div>

        <!-- Alert Messages -->
        {if $successmessage}
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-medium">{$successmessage}</span>
                </div>
            </div>
        {/if}

        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <span class="text-red-300 font-medium">{$errormessage}</span>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="user-cog" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Account Menu
                    </h3>
                    <nav class="space-y-2">
                        <a href="?action=details" class="flex items-center px-3 py-2 text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg font-medium">
                            <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                            Personal Details
                        </a>
                        <a href="?action=changepw" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="lock" class="w-4 h-4 mr-3"></i>
                            Change Password
                        </a>
                        <a href="?action=security" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-teal-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="shield" class="w-4 h-4 mr-3"></i>
                            Security Settings
                        </a>
                        <a href="?action=emails" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-3"></i>
                            Email Preferences
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <!-- Personal Details Form -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="user-check" class="w-5 h-5 mr-2 text-blue-400"></i>
                            Personal Information
                        </h2>
                        
                        <form method="post" action="{$smarty.server.PHP_SELF}?action=details" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-1">
                                    <label for="firstname" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="user" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                        {$LANG.clientareafirstname}
                                    </label>
                                    <input type="text" 
                                           name="firstname" 
                                           id="firstname" 
                                           value="{$clientfirstname}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300"
                                           required>
                                </div>
                                <div class="space-y-1">
                                    <label for="lastname" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="user" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                        {$LANG.clientarealastname}
                                    </label>
                                    <input type="text" 
                                           name="lastname" 
                                           id="lastname" 
                                           value="{$clientlastname}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300"
                                           required>
                                </div>
                            </div>

                            <div class="space-y-1">
                                <label for="email" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="mail" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                    {$LANG.clientareaemail}
                                </label>
                                <input type="email" 
                                       name="email" 
                                       id="email" 
                                       value="{$clientemail}"
                                       class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300"
                                       required>
                            </div>

                            <div class="space-y-1">
                                <label for="phonenumber" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="phone" class="w-4 h-4 mr-2 inline text-orange-400"></i>
                                    {$LANG.clientareaphonenumber}
                                </label>
                                <input type="tel" 
                                       name="phonenumber" 
                                       id="phonenumber" 
                                       value="{$clientphonenumber}"
                                       class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300">
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-1">
                                    <label for="companyname" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="building" class="w-4 h-4 mr-2 inline text-cyan-400"></i>
                                        {$LANG.clientareacompanyname}
                                    </label>
                                    <input type="text" 
                                           name="companyname" 
                                           id="companyname" 
                                           value="{$clientcompanyname}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-cyan-500/50 transition-all duration-300">
                                </div>
                                <div class="space-y-1">
                                    <label for="address1" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="map-pin" class="w-4 h-4 mr-2 inline text-pink-400"></i>
                                        {$LANG.clientareaaddress1}
                                    </label>
                                    <input type="text" 
                                           name="address1" 
                                           id="address1" 
                                           value="{$clientaddress1}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500/50 focus:border-pink-500/50 transition-all duration-300">
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="space-y-1">
                                    <label for="city" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="map" class="w-4 h-4 mr-2 inline text-teal-400"></i>
                                        {$LANG.clientareacity}
                                    </label>
                                    <input type="text" 
                                           name="city" 
                                           id="city" 
                                           value="{$clientcity}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-teal-500/50 transition-all duration-300">
                                </div>
                                <div class="space-y-1">
                                    <label for="state" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="flag" class="w-4 h-4 mr-2 inline text-indigo-400"></i>
                                        {$LANG.clientareastate}
                                    </label>
                                    <input type="text" 
                                           name="state" 
                                           id="state" 
                                           value="{$clientstate}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500/50 transition-all duration-300">
                                </div>
                                <div class="space-y-1">
                                    <label for="postcode" class="block text-sm font-medium text-gray-300">
                                        <i data-lucide="hash" class="w-4 h-4 mr-2 inline text-violet-400"></i>
                                        {$LANG.clientareapostcode}
                                    </label>
                                    <input type="text" 
                                           name="postcode" 
                                           id="postcode" 
                                           value="{$clientpostcode}"
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500/50 transition-all duration-300">
                                </div>
                            </div>

                            <div class="space-y-1">
                                <label for="country" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="globe" class="w-4 h-4 mr-2 inline text-rose-400"></i>
                                    {$LANG.clientareacountry}
                                </label>
                                <select name="country" 
                                        id="country"
                                        class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-rose-500/50 focus:border-rose-500/50 transition-all duration-300">
                                    {foreach $countries as $countrycode => $countryname}
                                        <option value="{$countrycode}"{if $clientcountry eq $countrycode} selected{/if}>{$countryname}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="pt-4">
                                <button type="submit" 
                                        name="save" 
                                        value="Save Changes"
                                        class="w-full md:w-auto bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                                    <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                                    {$LANG.clientareasavechanges}
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Account Statistics -->
                    <div class="border-t border-slate-700/50 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2 text-emerald-400"></i>
                            Account Overview
                        </h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-4 text-center">
                                <div class="text-2xl font-bold text-blue-400 mb-1">{$numactiveproducts}</div>
                                <div class="text-sm text-gray-400">Active Services</div>
                            </div>
                            <div class="bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 rounded-xl p-4 text-center">
                                <div class="text-2xl font-bold text-emerald-400 mb-1">{$numtickets}</div>
                                <div class="text-sm text-gray-400">Support Tickets</div>
                            </div>
                            <div class="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl p-4 text-center">
                                <div class="text-2xl font-bold text-orange-400 mb-1">{$numinvoices}</div>
                                <div class="text-sm text-gray-400">Total Invoices</div>
                            </div>
                            <div class="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-4 text-center">
                                <div class="text-2xl font-bold text-purple-400 mb-1">{$credit}</div>
                                <div class="text-sm text-gray-400">Account Credit</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{include file="$template/footer.tpl"}