<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="{$charset}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEST - {$companyname} - Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #020617;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 400px;
            margin: 50px auto;
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 30px;
        }
        .test-header {
            background: #1e293b;
            color: white;
            padding: 10px;
            margin: -30px -30px 20px -30px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .test-footer {
            background: #1e293b;
            color: white;
            padding: 10px;
            margin: 20px -30px -30px -30px;
            border-radius: 0 0 8px 8px;
            text-align: center;
            font-size: 12px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #d1d5db;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: white;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #0ea5e9;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0284c7;
        }
        .debug-info {
            background: #374151;
            padding: 10px;
            margin: 20px 0;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
    {$headoutput}
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>TEST LOGIN PAGE</h1>
            <p>This is a minimal test template</p>
        </div>

        <div class="debug-info">
            <strong>Debug Info:</strong><br>
            Template: {$template}<br>
            Company: {$companyname}<br>
            Page: Login Test<br>
            Time: {$smarty.now|date_format:"%Y-%m-%d %H:%M:%S"}
        </div>

        {if $incorrect}
            <div style="background: #dc2626; padding: 10px; margin-bottom: 15px; border-radius: 4px;">
                {$LANG.loginincorrect}
            </div>
        {/if}

        <form method="post" action="{$systemurl}dologin.php">
            <div class="form-group">
                <label for="inputEmail">{$LANG.clientareaemail}</label>
                <input type="email" name="username" id="inputEmail" required autofocus>
            </div>

            <div class="form-group">
                <label for="inputPassword">{$LANG.clientareapassword}</label>
                <input type="password" name="password" id="inputPassword" required>
            </div>

            {if $captcha}
                <div class="form-group">
                    <label>Security Verification</label>
                    {$captcha}
                </div>
            {/if}

            <button type="submit">{$LANG.loginbutton}</button>
        </form>

        <div class="test-footer">
            TEST FOOTER - &copy; {$date_year} {$companyname}
        </div>
    </div>

    <script>
        console.log('Test login template loaded');
        console.log('Current time:', new Date().toISOString());
    </script>
</body>
</html>
