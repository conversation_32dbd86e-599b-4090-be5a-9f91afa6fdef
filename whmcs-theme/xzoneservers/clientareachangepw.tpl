<!-- Change Password Section -->
<section class="py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center gap-3 mb-4">
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="lock" class="w-3 h-3 mr-1.5"></i>
                    Password Security
                </div>
                <div class="flex items-center px-3 py-1.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-full text-xs font-bold text-white shadow-lg">
                    <i data-lucide="shield-check" class="w-3 h-3 mr-1.5"></i>
                    Account Protection
                </div>
            </div>
            
            <h1 class="text-3xl md:text-4xl font-black leading-tight mb-3">
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 via-pink-500 to-orange-500 bg-clip-text text-transparent">
                    Change Password
                </span>
            </h1>
            <p class="text-lg text-gray-300">Update your account password to keep your account secure</p>
        </div>

        <!-- Alert Messages -->
        {if $successmessage}
            <div class="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-3 text-emerald-400"></i>
                    <span class="text-emerald-300 font-medium">{$successmessage}</span>
                </div>
            </div>
        {/if}

        {if $errormessage}
            <div class="bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
                <div class="flex items-center">
                    <i data-lucide="alert-circle" class="w-5 h-5 mr-3 text-red-400"></i>
                    <span class="text-red-300 font-medium">{$errormessage}</span>
                </div>
            </div>
        {/if}

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Sidebar Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl sticky top-24">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i data-lucide="user-cog" class="w-5 h-5 mr-2 text-blue-400"></i>
                        Account Menu
                    </h3>
                    <nav class="space-y-2">
                        <a href="?action=details" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="user" class="w-4 h-4 mr-3"></i>
                            Personal Details
                        </a>
                        <a href="?action=changepw" class="flex items-center px-3 py-2 text-white bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg font-medium">
                            <i data-lucide="lock" class="w-4 h-4 mr-3"></i>
                            Change Password
                        </a>
                        <a href="?action=security" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-teal-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="shield" class="w-4 h-4 mr-3"></i>
                            Security Settings
                        </a>
                        <a href="?action=emails" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-lg transition-all duration-300">
                            <i data-lucide="mail" class="w-4 h-4 mr-3"></i>
                            Email Preferences
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-slate-900/70 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 shadow-2xl">
                    <!-- Change Password Form -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="key" class="w-5 h-5 mr-2 text-purple-400"></i>
                            Update Your Password
                        </h2>
                        <p class="text-gray-400 text-sm mb-6">
                            Choose a strong password to protect your account. Your password should be at least 8 characters long and contain a mix of letters, numbers, and symbols.
                        </p>
                        
                        <form method="post" action="{$smarty.server.PHP_SELF}?action=changepw" class="space-y-6" id="changePasswordForm">
                            <div class="space-y-1">
                                <label for="existingpassword" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="lock" class="w-4 h-4 mr-2 inline text-blue-400"></i>
                                    Current Password <span class="text-red-400">*</span>
                                </label>
                                <div class="relative">
                                    <input type="password" 
                                           name="existingpassword" 
                                           id="existingpassword" 
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 pr-10"
                                           placeholder="Enter your current password"
                                           required>
                                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="existingpassword">
                                        <i data-lucide="eye" class="w-4 h-4 text-gray-400 hover:text-white transition-colors"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="space-y-1">
                                <label for="newpassword" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="key" class="w-4 h-4 mr-2 inline text-purple-400"></i>
                                    New Password <span class="text-red-400">*</span>
                                </label>
                                <div class="relative">
                                    <input type="password" 
                                           name="newpassword" 
                                           id="newpassword" 
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500/50 transition-all duration-300 pr-10"
                                           placeholder="Enter your new password"
                                           required>
                                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="newpassword">
                                        <i data-lucide="eye" class="w-4 h-4 text-gray-400 hover:text-white transition-colors"></i>
                                    </button>
                                </div>
                                <!-- Password Strength Indicator -->
                                <div class="mt-2">
                                    <div class="flex items-center space-x-2">
                                        <div class="flex-1">
                                            <div class="h-2 bg-slate-700 rounded-full overflow-hidden">
                                                <div id="passwordStrength" class="h-full transition-all duration-300 rounded-full" style="width: 0%;"></div>
                                            </div>
                                        </div>
                                        <span id="passwordStrengthText" class="text-xs text-gray-400">Weak</span>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-1">
                                <label for="newpassword2" class="block text-sm font-medium text-gray-300">
                                    <i data-lucide="check-circle" class="w-4 h-4 mr-2 inline text-emerald-400"></i>
                                    Confirm New Password <span class="text-red-400">*</span>
                                </label>
                                <div class="relative">
                                    <input type="password" 
                                           name="newpassword2" 
                                           id="newpassword2" 
                                           class="w-full px-3 py-2.5 bg-slate-800/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all duration-300 pr-10"
                                           placeholder="Confirm your new password"
                                           required>
                                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="newpassword2">
                                        <i data-lucide="eye" class="w-4 h-4 text-gray-400 hover:text-white transition-colors"></i>
                                    </button>
                                </div>
                                <div id="passwordMatch" class="text-xs mt-1 hidden">
                                    <span id="passwordMatchText"></span>
                                </div>
                            </div>

                            <!-- Password Requirements -->
                            <div class="bg-slate-800/30 border border-slate-600/30 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-white mb-3 flex items-center">
                                    <i data-lucide="info" class="w-4 h-4 mr-2 text-blue-400"></i>
                                    Password Requirements
                                </h4>
                                <ul class="space-y-2 text-sm">
                                    <li class="flex items-center" id="req-length">
                                        <i data-lucide="circle" class="w-3 h-3 mr-2 text-gray-500"></i>
                                        <span class="text-gray-400">At least 8 characters long</span>
                                    </li>
                                    <li class="flex items-center" id="req-uppercase">
                                        <i data-lucide="circle" class="w-3 h-3 mr-2 text-gray-500"></i>
                                        <span class="text-gray-400">Contains uppercase letter (A-Z)</span>
                                    </li>
                                    <li class="flex items-center" id="req-lowercase">
                                        <i data-lucide="circle" class="w-3 h-3 mr-2 text-gray-500"></i>
                                        <span class="text-gray-400">Contains lowercase letter (a-z)</span>
                                    </li>
                                    <li class="flex items-center" id="req-number">
                                        <i data-lucide="circle" class="w-3 h-3 mr-2 text-gray-500"></i>
                                        <span class="text-gray-400">Contains number (0-9)</span>
                                    </li>
                                    <li class="flex items-center" id="req-special">
                                        <i data-lucide="circle" class="w-3 h-3 mr-2 text-gray-500"></i>
                                        <span class="text-gray-400">Contains special character (!@#$%^&*)</span>
                                    </li>
                                </ul>
                            </div>

                            <div class="pt-4">
                                <button type="submit" 
                                        name="submit" 
                                        value="Save Changes"
                                        class="w-full md:w-auto bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 text-white px-8 py-3 rounded-lg font-bold shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-slate-900">
                                    <i data-lucide="save" class="w-4 h-4 mr-2 inline"></i>
                                    Update Password
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Security Tips -->
                    <div class="border-t border-slate-700/50 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i data-lucide="shield-alert" class="w-5 h-5 mr-2 text-yellow-400"></i>
                            Security Tips
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg flex items-center justify-center mr-3 mt-1">
                                        <i data-lucide="eye-off" class="w-4 h-4 text-blue-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white text-sm mb-1">Use Unique Passwords</h4>
                                        <p class="text-xs text-gray-400">Don't reuse passwords from other accounts or services.</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-gradient-to-br from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 rounded-xl p-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 border border-emerald-500/30 rounded-lg flex items-center justify-center mr-3 mt-1">
                                        <i data-lucide="smartphone" class="w-4 h-4 text-emerald-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white text-sm mb-1">Enable 2FA</h4>
                                        <p class="text-xs text-gray-400">Add an extra layer of security to your account.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl p-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-500/30 rounded-lg flex items-center justify-center mr-3 mt-1">
                                        <i data-lucide="clock" class="w-4 h-4 text-orange-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white text-sm mb-1">Update Regularly</h4>
                                        <p class="text-xs text-gray-400">Change your password every 3-6 months.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl p-4">
                                <div class="flex items-start">
                                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-lg flex items-center justify-center mr-3 mt-1">
                                        <i data-lucide="key" class="w-4 h-4 text-purple-400"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-white text-sm mb-1">Password Manager</h4>
                                        <p class="text-xs text-gray-400">Consider using a password manager for better security.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetInput = document.getElementById(targetId);
            const icon = this.querySelector('i');
            
            if (targetInput.type === 'password') {
                targetInput.type = 'text';
                icon.setAttribute('data-lucide', 'eye-off');
            } else {
                targetInput.type = 'password';
                icon.setAttribute('data-lucide', 'eye');
            }
            
            // Refresh Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    });

    // Password strength checker
    const newPasswordInput = document.getElementById('newpassword');
    const confirmPasswordInput = document.getElementById('newpassword2');
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');
    const passwordMatch = document.getElementById('passwordMatch');
    const passwordMatchText = document.getElementById('passwordMatchText');

    function updateRequirement(id, passed) {
        const element = document.getElementById(id);
        const icon = element.querySelector('i');
        const span = element.querySelector('span');
        
        if (passed) {
            icon.setAttribute('data-lucide', 'check-circle');
            icon.className = 'w-3 h-3 mr-2 text-emerald-400';
            span.className = 'text-emerald-300';
        } else {
            icon.setAttribute('data-lucide', 'circle');
            icon.className = 'w-3 h-3 mr-2 text-gray-500';
            span.className = 'text-gray-400';
        }
        
        // Refresh Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    function checkPasswordStrength(password) {
        let score = 0;
        const requirements = {
            'req-length': password.length >= 8,
            'req-uppercase': /[A-Z]/.test(password),
            'req-lowercase': /[a-z]/.test(password),
            'req-number': /[0-9]/.test(password),
            'req-special': /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
        };

        Object.keys(requirements).forEach(req => {
            updateRequirement(req, requirements[req]);
            if (requirements[req]) score++;
        });

        const percentage = (score / 5) * 100;
        strengthBar.style.width = percentage + '%';

        if (score <= 2) {
            strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-red-500';
            strengthText.textContent = 'Weak';
            strengthText.className = 'text-xs text-red-400';
        } else if (score <= 3) {
            strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-yellow-500';
            strengthText.textContent = 'Fair';
            strengthText.className = 'text-xs text-yellow-400';
        } else if (score <= 4) {
            strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-blue-500';
            strengthText.textContent = 'Good';
            strengthText.className = 'text-xs text-blue-400';
        } else {
            strengthBar.className = 'h-full transition-all duration-300 rounded-full bg-emerald-500';
            strengthText.textContent = 'Strong';
            strengthText.className = 'text-xs text-emerald-400';
        }
    }

    function checkPasswordMatch() {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (confirmPassword.length > 0) {
            passwordMatch.classList.remove('hidden');
            
            if (newPassword === confirmPassword) {
                passwordMatchText.innerHTML = '<i data-lucide="check-circle" class="w-3 h-3 mr-1 inline text-emerald-400"></i><span class="text-emerald-300">Passwords match</span>';
            } else {
                passwordMatchText.innerHTML = '<i data-lucide="x-circle" class="w-3 h-3 mr-1 inline text-red-400"></i><span class="text-red-300">Passwords do not match</span>';
            }
            
            // Refresh Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        } else {
            passwordMatch.classList.add('hidden');
        }
    }

    newPasswordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        checkPasswordMatch();
    });

    confirmPasswordInput.addEventListener('input', checkPasswordMatch);

    // Form validation
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        const newPassword = newPasswordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('New passwords do not match. Please check and try again.');
            return false;
        }
        
        if (newPassword.length < 8) {
            e.preventDefault();
            alert('Password must be at least 8 characters long.');
            return false;
        }
    });
});
</script>